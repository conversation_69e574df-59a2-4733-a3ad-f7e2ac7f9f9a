/**
 * @file system_init.h
 * @brief 系统初始化接口定义
 * <AUTHOR> Sync System Team
 * @date 2025-07-25
 */

#ifndef SYSTEM_INIT_H
#define SYSTEM_INIT_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>

/* ========== 系统初始化接口 ========== */

/**
 * @brief 系统基础初始化
 * @return 0: 成功, 负数: 错误码
 */
int system_init(void);

/**
 * @brief 系统去初始化
 */
void system_deinit(void);

/**
 * @brief 获取系统时钟频率
 * @return 系统时钟频率 (Hz)
 */
uint32_t system_get_clock_freq(void);

/**
 * @brief 获取系统运行时间
 * @return 系统运行时间 (ms)
 */
uint32_t system_get_tick(void);

/**
 * @brief 系统延时
 * @param ms 延时时间 (ms)
 */
void system_delay_ms(uint32_t ms);

/**
 * @brief 系统延时 (微秒)
 * @param us 延时时间 (us)
 */
void system_delay_us(uint32_t us);

/**
 * @brief 获取空闲堆内存大小
 * @return 空闲堆内存大小 (bytes)
 */
uint32_t system_get_free_heap(void);

/**
 * @brief 系统任务调度
 */
void system_task_schedule(void);

/**
 * @brief 看门狗喂狗
 */
void system_watchdog_feed(void);

/**
 * @brief 系统复位
 */
void system_reset(void);

/**
 * @brief 进入低功耗模式
 */
void system_enter_sleep(void);

/**
 * @brief 退出低功耗模式
 */
void system_exit_sleep(void);

/* ========== 中断管理接口 ========== */

/**
 * @brief 全局中断使能
 */
void system_irq_enable(void);

/**
 * @brief 全局中断禁止
 */
void system_irq_disable(void);

/**
 * @brief 保存中断状态并禁止中断
 * @return 中断状态
 */
uint32_t system_irq_save(void);

/**
 * @brief 恢复中断状态
 * @param irq_state 中断状态
 */
void system_irq_restore(uint32_t irq_state);

/* ========== 内存管理接口 ========== */

/**
 * @brief 分配内存
 * @param size 内存大小
 * @return 内存指针，失败返回NULL
 */
void* system_malloc(size_t size);

/**
 * @brief 释放内存
 * @param ptr 内存指针
 */
void system_free(void* ptr);

/**
 * @brief 重新分配内存
 * @param ptr 原内存指针
 * @param size 新内存大小
 * @return 新内存指针，失败返回NULL
 */
void* system_realloc(void* ptr, size_t size);

/* ========== 错误码定义 ========== */
#define SYSTEM_OK                0
#define SYSTEM_ERROR            -1
#define SYSTEM_ERROR_PARAM      -2
#define SYSTEM_ERROR_MEMORY     -3
#define SYSTEM_ERROR_TIMEOUT    -4
#define SYSTEM_ERROR_BUSY       -5
#define SYSTEM_ERROR_NOT_READY  -6
#define SYSTEM_ERROR_NOT_SUPPORT -7

/* ========== 系统状态定义 ========== */
typedef enum {
    SYSTEM_STATE_INIT = 0,
    SYSTEM_STATE_RUNNING,
    SYSTEM_STATE_SLEEP,
    SYSTEM_STATE_ERROR
} system_state_t;

/**
 * @brief 获取系统状态
 * @return 系统状态
 */
system_state_t system_get_state(void);

/**
 * @brief 设置系统状态
 * @param state 系统状态
 */
void system_set_state(system_state_t state);

#ifdef __cplusplus
}
#endif

#endif /* SYSTEM_INIT_H */
