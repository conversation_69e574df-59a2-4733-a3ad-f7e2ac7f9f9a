/**
 * @file color_extract.h
 * @brief 颜色提取算法接口定义
 * <AUTHOR> Sync System Team
 * @date 2025-07-25
 */

#ifndef COLOR_EXTRACT_H
#define COLOR_EXTRACT_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "system_config.h"
#include "drivers/camera_driver.h"
#include "drivers/led_driver.h"

/* ========== 颜色提取数据结构 ========== */

/**
 * @brief 颜色统计信息
 */
typedef struct {
    led_rgb_t color;        /**< 颜色值 */
    uint32_t count;         /**< 像素数量 */
    float percentage;       /**< 占比 */
} color_stat_t;

/**
 * @brief 颜色提取结果
 */
typedef struct {
    led_rgb_t dominant_color;       /**< 主色调 */
    led_rgb_t average_color;        /**< 平均颜色 */
    color_stat_t top_colors[5];     /**< 前5种颜色 */
    uint8_t color_count;            /**< 有效颜色数量 */
    float brightness;               /**< 整体亮度 */
    float contrast;                 /**< 对比度 */
    float saturation;               /**< 饱和度 */
} color_extract_result_t;

/**
 * @brief 区域颜色信息
 */
typedef struct {
    uint16_t x;                     /**< 区域X坐标 */
    uint16_t y;                     /**< 区域Y坐标 */
    uint16_t width;                 /**< 区域宽度 */
    uint16_t height;                /**< 区域高度 */
    led_rgb_t color;                /**< 区域主色调 */
    float weight;                   /**< 权重 */
} region_color_t;

/**
 * @brief 颜色提取配置
 */
typedef struct {
    color_extract_method_t method;  /**< 提取方法 */
    uint8_t cluster_count;          /**< 聚类数量 */
    uint8_t sample_rate;            /**< 采样率 */
    bool edge_detection;            /**< 边缘检测使能 */
    uint8_t edge_threshold;         /**< 边缘阈值 */
    bool color_smooth;              /**< 颜色平滑使能 */
    float smooth_factor;            /**< 平滑因子 */
    uint8_t region_count;           /**< 区域数量 */
    bool brightness_adapt;          /**< 亮度自适应 */
} color_extract_config_t;

/**
 * @brief 颜色提取统计
 */
typedef struct {
    uint32_t total_frames;          /**< 总帧数 */
    uint32_t processed_frames;      /**< 已处理帧数 */
    uint32_t avg_process_time;      /**< 平均处理时间 (us) */
    uint32_t max_process_time;      /**< 最大处理时间 (us) */
    uint32_t min_process_time;      /**< 最小处理时间 (us) */
} color_extract_stats_t;

/* ========== 颜色提取算法接口 ========== */

/**
 * @brief 颜色提取算法初始化
 * @return 0: 成功, 负数: 错误码
 */
int color_extract_init(void);

/**
 * @brief 颜色提取算法去初始化
 */
void color_extract_deinit(void);

/**
 * @brief 设置颜色提取配置
 * @param config 配置参数
 * @return 0: 成功, 负数: 错误码
 */
int color_extract_set_config(const color_extract_config_t* config);

/**
 * @brief 获取颜色提取配置
 * @param config 配置参数输出
 * @return 0: 成功, 负数: 错误码
 */
int color_extract_get_config(color_extract_config_t* config);

/**
 * @brief 从图像帧提取颜色
 * @param frame 图像帧
 * @param result 提取结果输出
 * @return 0: 成功, 负数: 错误码
 */
int color_extract_from_frame(const camera_frame_t* frame, 
                             color_extract_result_t* result);

/**
 * @brief 从图像数据提取颜色
 * @param data 图像数据
 * @param width 图像宽度
 * @param height 图像高度
 * @param format 像素格式
 * @param result 提取结果输出
 * @return 0: 成功, 负数: 错误码
 */
int color_extract_from_data(const uint8_t* data, uint16_t width, uint16_t height,
                            camera_format_t format, color_extract_result_t* result);

/**
 * @brief 区域颜色提取
 * @param frame 图像帧
 * @param regions 区域数组
 * @param region_count 区域数量
 * @return 0: 成功, 负数: 错误码
 */
int color_extract_regions(const camera_frame_t* frame, 
                          region_color_t* regions, uint8_t region_count);

/**
 * @brief 获取统计信息
 * @param stats 统计信息输出
 * @return 0: 成功, 负数: 错误码
 */
int color_extract_get_stats(color_extract_stats_t* stats);

/**
 * @brief 重置统计信息
 */
void color_extract_reset_stats(void);

/* ========== 颜色算法实现接口 ========== */

/**
 * @brief 平均颜色算法
 * @param data 图像数据
 * @param width 图像宽度
 * @param height 图像高度
 * @param format 像素格式
 * @param sample_rate 采样率
 * @return 平均颜色
 */
led_rgb_t color_algorithm_average(const uint8_t* data, uint16_t width, uint16_t height,
                                  camera_format_t format, uint8_t sample_rate);

/**
 * @brief 主色调算法 (直方图)
 * @param data 图像数据
 * @param width 图像宽度
 * @param height 图像高度
 * @param format 像素格式
 * @param sample_rate 采样率
 * @return 主色调
 */
led_rgb_t color_algorithm_dominant(const uint8_t* data, uint16_t width, uint16_t height,
                                   camera_format_t format, uint8_t sample_rate);

/**
 * @brief K-means聚类算法
 * @param data 图像数据
 * @param width 图像宽度
 * @param height 图像高度
 * @param format 像素格式
 * @param cluster_count 聚类数量
 * @param sample_rate 采样率
 * @param colors 输出颜色数组
 * @return 有效颜色数量
 */
int color_algorithm_kmeans(const uint8_t* data, uint16_t width, uint16_t height,
                           camera_format_t format, uint8_t cluster_count, 
                           uint8_t sample_rate, led_rgb_t* colors);

/* ========== 边缘检测接口 ========== */

/**
 * @brief Sobel边缘检测
 * @param data 图像数据 (灰度)
 * @param width 图像宽度
 * @param height 图像高度
 * @param threshold 边缘阈值
 * @param edge_map 边缘图输出
 * @return 0: 成功, 负数: 错误码
 */
int edge_detect_sobel(const uint8_t* data, uint16_t width, uint16_t height,
                      uint8_t threshold, uint8_t* edge_map);

/**
 * @brief Canny边缘检测
 * @param data 图像数据 (灰度)
 * @param width 图像宽度
 * @param height 图像高度
 * @param low_threshold 低阈值
 * @param high_threshold 高阈值
 * @param edge_map 边缘图输出
 * @return 0: 成功, 负数: 错误码
 */
int edge_detect_canny(const uint8_t* data, uint16_t width, uint16_t height,
                      uint8_t low_threshold, uint8_t high_threshold, uint8_t* edge_map);

/* ========== 颜色平滑接口 ========== */

/**
 * @brief 颜色平滑滤波器初始化
 * @param factor 平滑因子 (0.0-1.0)
 * @return 0: 成功, 负数: 错误码
 */
int color_smooth_init(float factor);

/**
 * @brief 颜色平滑处理
 * @param current_color 当前颜色
 * @return 平滑后颜色
 */
led_rgb_t color_smooth_filter(led_rgb_t current_color);

/**
 * @brief 重置颜色平滑滤波器
 */
void color_smooth_reset(void);

/* ========== 颜色空间转换接口 ========== */

/**
 * @brief RGB565转RGB888
 * @param rgb565 RGB565颜色
 * @return RGB888颜色
 */
led_rgb_t color_rgb565_to_rgb888(uint16_t rgb565);

/**
 * @brief YUV422转RGB888
 * @param y Y分量
 * @param u U分量
 * @param v V分量
 * @return RGB888颜色
 */
led_rgb_t color_yuv422_to_rgb888(uint8_t y, uint8_t u, uint8_t v);

/**
 * @brief RGB转灰度
 * @param rgb RGB颜色
 * @return 灰度值
 */
uint8_t color_rgb_to_gray(led_rgb_t rgb);

/* ========== 颜色分析工具 ========== */

/**
 * @brief 计算颜色亮度
 * @param rgb RGB颜色
 * @return 亮度值 (0.0-1.0)
 */
float color_calculate_brightness(led_rgb_t rgb);

/**
 * @brief 计算颜色饱和度
 * @param rgb RGB颜色
 * @return 饱和度值 (0.0-1.0)
 */
float color_calculate_saturation(led_rgb_t rgb);

/**
 * @brief 计算颜色对比度
 * @param rgb1 颜色1
 * @param rgb2 颜色2
 * @return 对比度值 (0.0-1.0)
 */
float color_calculate_contrast(led_rgb_t rgb1, led_rgb_t rgb2);

/**
 * @brief 颜色距离计算
 * @param rgb1 颜色1
 * @param rgb2 颜色2
 * @return 颜色距离
 */
float color_calculate_distance(led_rgb_t rgb1, led_rgb_t rgb2);

/* ========== 错误码定义 ========== */
#define COLOR_EXTRACT_OK                0
#define COLOR_EXTRACT_ERROR            -1
#define COLOR_EXTRACT_ERROR_PARAM      -2
#define COLOR_EXTRACT_ERROR_MEMORY     -3
#define COLOR_EXTRACT_ERROR_TIMEOUT    -4
#define COLOR_EXTRACT_ERROR_NOT_READY  -5
#define COLOR_EXTRACT_ERROR_FORMAT     -6

#ifdef __cplusplus
}
#endif

#endif /* COLOR_EXTRACT_H */
