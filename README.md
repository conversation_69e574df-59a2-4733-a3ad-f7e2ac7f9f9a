# LED背景灯同步系统

基于HPM5321主控的电视背景灯同步LED系统，通过摄像头实时采集电视屏幕颜色，并同步控制LED灯带显示相应的氛围灯效果。

## 项目特性

- **高性能主控**: 基于HPM5321 RISC-V 32位微控制器
- **高清采集**: 720p摄像头实时图像采集
- **智能算法**: 先进的颜色提取和分析算法
- **无线控制**: 蓝牙BLE 5.0无线通信
- **多种效果**: 支持多种LED效果和同步模式
- **低延迟**: 端到端延迟小于100ms

## 系统架构

```
摄像头 → HPM5321主控 → LED灯带
   ↑         ↓
蓝牙模块 ← 手机APP
```

### 硬件组成
- HPM5321主控芯片
- 720p USB摄像头
- WS2812B RGB LED灯带
- 蓝牙BLE模块
- 电源管理模块

### 软件架构
- **驱动层**: 摄像头、LED、蓝牙驱动
- **算法层**: 颜色提取、边缘检测、平滑滤波
- **应用层**: 同步引擎、配置管理
- **通信层**: BLE协议栈、命令处理

## 快速开始

### 环境要求
- HPM SDK (版本 >= 1.0.0)
- CMake (版本 >= 3.16)
- GCC工具链 (RISC-V)

### 编译步骤

1. 克隆项目
```bash
git clone <repository_url>
cd led_control
```

2. 配置SDK路径
```bash
# 修改CMakeLists.txt中的HPM_SDK_BASE路径
export HPM_SDK_BASE=/path/to/hpm_sdk
```

3. 编译项目
```bash
mkdir build
cd build
cmake ..
make
```

4. 烧录固件
```bash
# 使用OpenOCD或其他烧录工具
openocd -f hpm5321.cfg -c "program led_sync_system.elf verify reset exit"
```

## 目录结构

```
led_control/
├── CMakeLists.txt          # CMake构建配置
├── README.md               # 项目说明
├── config/                 # 配置文件
│   └── system_config.h     # 系统配置
├── docs/                   # 文档
│   ├── requirements_and_architecture.md  # 需求和架构文档
│   └── api_reference.md    # API参考文档
├── src/                    # 源代码
│   ├── main.c              # 主程序
│   ├── common/             # 公共模块
│   │   ├── system_init.h   # 系统初始化
│   │   └── debug.h         # 调试接口
│   ├── drivers/            # 驱动层
│   │   ├── camera_driver.h # 摄像头驱动
│   │   ├── led_driver.h    # LED驱动
│   │   └── ble_driver.h    # 蓝牙驱动
│   ├── algorithm/          # 算法层
│   │   └── color_extract.h # 颜色提取算法
│   └── application/        # 应用层
│       └── sync_engine.h   # 同步引擎
└── tests/                  # 测试代码
    ├── unit_tests/         # 单元测试
    └── integration_tests/  # 集成测试
```

## 配置说明

### 系统配置
主要配置参数在 `config/system_config.h` 中定义：

- `CAMERA_WIDTH/HEIGHT`: 摄像头分辨率
- `LED_COUNT_DEFAULT`: 默认LED数量
- `BLE_DEVICE_NAME`: 蓝牙设备名称
- `COLOR_EXTRACT_METHOD`: 颜色提取算法

### 运行时配置
通过蓝牙APP可以实时调节：
- 亮度和饱和度
- 同步模式
- LED映射方式
- 颜色效果参数

## API参考

### 主要接口

#### 摄像头驱动
```c
int camera_init(void);
camera_frame_t* camera_get_frame(void);
void camera_release_frame(camera_frame_t* frame);
```

#### LED驱动
```c
int led_init(void);
int led_set_pixel(uint16_t index, led_rgb_t color);
int led_update(void);
```

#### 颜色提取
```c
int color_extract_from_frame(const camera_frame_t* frame, 
                             color_extract_result_t* result);
```

#### 同步引擎
```c
int sync_engine_init(void);
int sync_engine_process_frame(const camera_frame_t* frame);
int sync_engine_set_config(const sync_engine_config_t* config);
```

## 性能指标

- **处理延迟**: < 100ms (端到端)
- **帧率**: 30fps (720p)
- **CPU占用**: < 80%
- **内存使用**: < 256KB RAM
- **功耗**: < 5W

## 调试和测试

### 调试输出
通过UART输出调试信息：
```c
DEBUG_INFO("System started");
DEBUG_ERROR("Camera init failed: %d", ret);
```

### 测试功能
- LED测试序列
- 颜色提取测试
- 性能基准测试
- 蓝牙通信测试

### 监控工具
- 实时FPS显示
- 内存使用监控
- 错误统计
- 性能分析

## 故障排除

### 常见问题

1. **摄像头无法识别**
   - 检查USB连接
   - 确认摄像头兼容性
   - 检查驱动配置

2. **LED不亮**
   - 检查电源供电
   - 确认SPI连接
   - 检查LED类型配置

3. **蓝牙连接失败**
   - 检查蓝牙模块
   - 确认UART配置
   - 重置蓝牙模块

4. **颜色同步延迟**
   - 调整处理参数
   - 优化算法配置
   - 检查系统负载

## 开发计划

- [x] 项目架构设计
- [x] 工程框架搭建
- [ ] 摄像头驱动开发
- [ ] 颜色提取算法实现
- [ ] LED驱动开发
- [ ] 蓝牙通信实现
- [ ] 系统集成测试
- [ ] 性能优化
- [ ] 文档完善

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

- 项目维护者: LED Sync System Team
- 邮箱: <EMAIL>
- 技术支持: <EMAIL>
