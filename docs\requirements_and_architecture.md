# 电视背景灯同步LED系统 - 需求分析与架构设计

## 1. 项目概述

本项目旨在开发一个基于HPM5321主控的智能LED背景灯系统，通过摄像头实时采集电视屏幕颜色，并同步控制LED灯带显示相应的氛围灯效果。

### 1.1 核心功能
- 720p摄像头实时图像采集
- 智能颜色提取与分析算法
- RGB LED灯带驱动控制
- 蓝牙BLE无线通信
- 手机APP远程控制

### 1.2 技术指标
- 主控芯片：HPM5321 (RISC-V 32位高性能微控制器)
- 摄像头分辨率：720p (1280x720)
- LED类型：WS2812B RGB可编程LED
- 通信方式：蓝牙BLE 5.0
- 响应延迟：< 100ms
- 功耗：< 5W

## 2. 系统架构设计

### 2.1 硬件架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   720p Camera   │    │    HPM5321      │    │   LED Strip     │
│                 │────│   Main MCU      │────│   (WS2812B)     │
│   (USB/MIPI)    │    │                 │    │                 │
└─────────────────┘    │  ┌───────────┐  │    └─────────────────┘
                       │  │ Image     │  │
┌─────────────────┐    │  │ Process   │  │    ┌─────────────────┐
│  Bluetooth      │────│  │ Unit      │  │────│   Power         │
│  Module         │    │  └───────────┘  │    │   Management    │
│  (BLE 5.0)      │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 软件架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
├─────────────────────────────────────────────────────────────┤
│  Color Sync Engine  │  BLE Service  │  Configuration Mgr   │
├─────────────────────────────────────────────────────────────┤
│                    Algorithm Layer                          │
├─────────────────────────────────────────────────────────────┤
│ Color Extraction │ Edge Detection │ Smooth Filter │ Effects │
├─────────────────────────────────────────────────────────────┤
│                     Driver Layer                            │
├─────────────────────────────────────────────────────────────┤
│ Camera Driver │ LED Driver │ BLE Driver │ Timer Driver      │
├─────────────────────────────────────────────────────────────┤
│                      HAL Layer                              │
├─────────────────────────────────────────────────────────────┤
│    GPIO    │    SPI    │    I2C    │    UART    │   PWM    │
├─────────────────────────────────────────────────────────────┤
│                   HPM5321 Hardware                          │
└─────────────────────────────────────────────────────────────┘
```

## 3. 模块详细设计

### 3.1 摄像头采集模块 (Camera Module)
- **功能**：720p图像实时采集
- **接口**：USB 2.0 或 MIPI CSI
- **输出格式**：RGB565/YUV422
- **帧率**：30fps
- **缓冲区**：双缓冲机制

### 3.2 颜色提取算法模块 (Color Algorithm Module)
- **主色调提取**：K-means聚类算法
- **边缘检测**：Sobel算子
- **颜色平滑**：移动平均滤波
- **区域分析**：屏幕分区颜色提取

### 3.3 LED驱动模块 (LED Driver Module)
- **协议支持**：WS2812B时序协议
- **控制方式**：SPI + DMA
- **颜色深度**：24位RGB (8位/通道)
- **LED数量**：最大支持300颗LED

### 3.4 蓝牙通信模块 (BLE Module)
- **协议版本**：BLE 5.0
- **服务定义**：自定义GATT服务
- **功能**：参数配置、状态监控、固件升级

## 4. 接口定义

### 4.1 硬件接口
- **摄像头接口**：USB 2.0 Host
- **LED输出**：SPI (MOSI, SCLK)
- **蓝牙模块**：UART (TX, RX, CTS, RTS)
- **电源管理**：ADC (电压监测)
- **用户接口**：GPIO (按键, LED指示)

### 4.2 软件接口
```c
// 摄像头接口
typedef struct {
    uint16_t width;
    uint16_t height;
    uint8_t* frame_buffer;
    uint32_t frame_size;
} camera_frame_t;

// LED控制接口
typedef struct {
    uint8_t red;
    uint8_t green;
    uint8_t blue;
    uint8_t brightness;
} led_color_t;

// 蓝牙数据接口
typedef struct {
    uint8_t command;
    uint8_t length;
    uint8_t data[32];
} ble_packet_t;
```

## 5. 性能要求

### 5.1 实时性要求
- 图像采集延迟：< 33ms (30fps)
- 颜色处理延迟：< 50ms
- LED更新延迟：< 20ms
- 总体延迟：< 100ms

### 5.2 资源要求
- Flash存储：< 512KB
- RAM使用：< 256KB
- CPU占用率：< 80%
- 功耗：< 5W

## 6. 开发计划

1. **工程框架搭建** (1-2天)
2. **摄像头驱动开发** (2-3天)
3. **颜色算法实现** (3-4天)
4. **LED驱动开发** (2-3天)
5. **蓝牙通信实现** (2-3天)
6. **系统集成测试** (2-3天)
7. **性能优化** (1-2天)
8. **文档编写** (1天)

总开发周期：约15-20个工作日
