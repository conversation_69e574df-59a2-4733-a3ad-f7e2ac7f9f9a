/**
 * @file color_extract.c
 * @brief 颜色提取算法实现
 * <AUTHOR> Sync System Team
 * @date 2025-07-25
 */

#include "color_extract.h"
#include "common/debug.h"
#include "common/system_init.h"
#include <string.h>
#include <stdlib.h>
#include <math.h>

/* ========== 私有数据结构 ========== */

/**
 * @brief 颜色提取上下文
 */
typedef struct {
    color_extract_config_t config;      /**< 配置参数 */
    color_extract_stats_t stats;        /**< 统计信息 */
    led_rgb_t last_color;               /**< 上一次的颜色 */
    bool initialized;                   /**< 初始化标志 */
    uint8_t* work_buffer;               /**< 工作缓冲区 */
    uint32_t work_buffer_size;          /**< 工作缓冲区大小 */
} color_extract_context_t;

/**
 * @brief K-means聚类点
 */
typedef struct {
    led_rgb_t color;                    /**< 颜色值 */
    uint32_t count;                     /**< 像素数量 */
} kmeans_point_t;

/**
 * @brief K-means聚类中心
 */
typedef struct {
    float r, g, b;                      /**< 颜色分量 */
    uint32_t count;                     /**< 分配的点数量 */
} kmeans_center_t;

/* ========== 私有变量 ========== */
static color_extract_context_t g_color_ctx = {0};

/* ========== 私有函数声明 ========== */
static int color_extract_validate_config(const color_extract_config_t* config);
static uint32_t color_extract_sample_pixels(const uint8_t* data, uint16_t width, uint16_t height,
                                           camera_format_t format, uint8_t sample_rate,
                                           led_rgb_t* samples, uint32_t max_samples);
static void color_extract_update_stats(uint32_t process_time);
static int kmeans_cluster(const led_rgb_t* samples, uint32_t sample_count,
                         uint8_t cluster_count, led_rgb_t* centers);
static float color_distance_squared(led_rgb_t c1, led_rgb_t c2);
static void color_histogram_analyze(const led_rgb_t* samples, uint32_t sample_count,
                                   color_stat_t* top_colors, uint8_t max_colors);

/* ========== 公共接口实现 ========== */

/**
 * @brief 颜色提取算法初始化
 */
int color_extract_init(void)
{
    DEBUG_ENTER();
    
    if (g_color_ctx.initialized) {
        DEBUG_WARN("Color extract already initialized");
        return COLOR_EXTRACT_OK;
    }
    
    /* 清零上下文 */
    memset(&g_color_ctx, 0, sizeof(color_extract_context_t));
    
    /* 设置默认配置 */
    g_color_ctx.config.method = COLOR_EXTRACT_METHOD;
    g_color_ctx.config.cluster_count = COLOR_CLUSTER_COUNT;
    g_color_ctx.config.sample_rate = COLOR_SAMPLE_RATE;
    g_color_ctx.config.edge_detection = EDGE_DETECT_ENABLED;
    g_color_ctx.config.edge_threshold = EDGE_THRESHOLD;
    g_color_ctx.config.color_smooth = COLOR_SMOOTH_ENABLED;
    g_color_ctx.config.smooth_factor = COLOR_SMOOTH_FACTOR;
    g_color_ctx.config.region_count = 4;
    g_color_ctx.config.brightness_adapt = true;
    
    /* 分配工作缓冲区 */
    g_color_ctx.work_buffer_size = CAMERA_WIDTH * CAMERA_HEIGHT / 4; /* 采样后的大小 */
    g_color_ctx.work_buffer = (uint8_t*)system_malloc(g_color_ctx.work_buffer_size);
    if (g_color_ctx.work_buffer == NULL) {
        DEBUG_ERROR("Failed to allocate work buffer");
        return COLOR_EXTRACT_ERROR_MEMORY;
    }
    
    /* 初始化颜色平滑滤波器 */
    if (g_color_ctx.config.color_smooth) {
        color_smooth_init(g_color_ctx.config.smooth_factor);
    }
    
    g_color_ctx.initialized = true;
    
    DEBUG_INFO("Color extract initialized successfully");
    DEBUG_EXIT_RET(COLOR_EXTRACT_OK);
    return COLOR_EXTRACT_OK;
}

/**
 * @brief 颜色提取算法去初始化
 */
void color_extract_deinit(void)
{
    DEBUG_ENTER();
    
    if (!g_color_ctx.initialized) {
        DEBUG_WARN("Color extract not initialized");
        return;
    }
    
    /* 释放工作缓冲区 */
    if (g_color_ctx.work_buffer != NULL) {
        system_free(g_color_ctx.work_buffer);
        g_color_ctx.work_buffer = NULL;
    }
    
    /* 清零上下文 */
    memset(&g_color_ctx, 0, sizeof(color_extract_context_t));
    
    DEBUG_INFO("Color extract deinitialized");
    DEBUG_EXIT();
}

/**
 * @brief 设置颜色提取配置
 */
int color_extract_set_config(const color_extract_config_t* config)
{
    if (config == NULL) {
        return COLOR_EXTRACT_ERROR_PARAM;
    }
    
    DEBUG_ENTER();
    
    /* 验证配置参数 */
    int ret = color_extract_validate_config(config);
    if (ret != COLOR_EXTRACT_OK) {
        DEBUG_ERROR("Invalid config: %d", ret);
        return ret;
    }
    
    /* 保存配置 */
    memcpy(&g_color_ctx.config, config, sizeof(color_extract_config_t));
    
    /* 重新初始化颜色平滑滤波器 */
    if (g_color_ctx.config.color_smooth) {
        color_smooth_init(g_color_ctx.config.smooth_factor);
    }
    
    DEBUG_INFO("Color extract config updated");
    DEBUG_EXIT_RET(COLOR_EXTRACT_OK);
    return COLOR_EXTRACT_OK;
}

/**
 * @brief 获取颜色提取配置
 */
int color_extract_get_config(color_extract_config_t* config)
{
    if (config == NULL) {
        return COLOR_EXTRACT_ERROR_PARAM;
    }
    
    memcpy(config, &g_color_ctx.config, sizeof(color_extract_config_t));
    return COLOR_EXTRACT_OK;
}

/**
 * @brief 从图像帧提取颜色
 */
int color_extract_from_frame(const camera_frame_t* frame, color_extract_result_t* result)
{
    if (frame == NULL || result == NULL) {
        return COLOR_EXTRACT_ERROR_PARAM;
    }
    
    if (!g_color_ctx.initialized) {
        return COLOR_EXTRACT_ERROR_NOT_READY;
    }
    
    return color_extract_from_data(frame->data, frame->width, frame->height, 
                                   frame->format, result);
}

/**
 * @brief 从图像数据提取颜色
 */
int color_extract_from_data(const uint8_t* data, uint16_t width, uint16_t height,
                            camera_format_t format, color_extract_result_t* result)
{
    if (data == NULL || result == NULL || width == 0 || height == 0) {
        return COLOR_EXTRACT_ERROR_PARAM;
    }
    
    DEBUG_ENTER();
    
    uint32_t start_time = system_get_tick();
    
    /* 清零结果 */
    memset(result, 0, sizeof(color_extract_result_t));
    
    /* 采样像素 */
    uint32_t max_samples = g_color_ctx.work_buffer_size / sizeof(led_rgb_t);
    led_rgb_t* samples = (led_rgb_t*)g_color_ctx.work_buffer;
    
    uint32_t sample_count = color_extract_sample_pixels(data, width, height, format,
                                                       g_color_ctx.config.sample_rate,
                                                       samples, max_samples);
    
    if (sample_count == 0) {
        DEBUG_ERROR("No samples extracted");
        return COLOR_EXTRACT_ERROR;
    }
    
    /* 根据配置的方法提取颜色 */
    switch (g_color_ctx.config.method) {
        case COLOR_METHOD_AVERAGE:
            result->average_color = color_algorithm_average(data, width, height, format,
                                                           g_color_ctx.config.sample_rate);
            result->dominant_color = result->average_color;
            break;
            
        case COLOR_METHOD_DOMINANT:
            result->dominant_color = color_algorithm_dominant(data, width, height, format,
                                                             g_color_ctx.config.sample_rate);
            result->average_color = result->dominant_color;
            break;
            
        case COLOR_METHOD_KMEANS:
            {
                led_rgb_t centers[COLOR_CLUSTER_COUNT];
                int cluster_count = color_algorithm_kmeans(data, width, height, format,
                                                          g_color_ctx.config.cluster_count,
                                                          g_color_ctx.config.sample_rate, centers);
                if (cluster_count > 0) {
                    result->dominant_color = centers[0]; /* 第一个聚类中心作为主色调 */
                    result->average_color = centers[0];
                    
                    /* 填充前几种颜色 */
                    for (int i = 0; i < cluster_count && i < 5; i++) {
                        result->top_colors[i].color = centers[i];
                        result->top_colors[i].percentage = 100.0f / cluster_count; /* 简化处理 */
                    }
                    result->color_count = cluster_count;
                }
            }
            break;
            
        default:
            DEBUG_ERROR("Unsupported color extract method: %d", g_color_ctx.config.method);
            return COLOR_EXTRACT_ERROR_NOT_SUPPORT;
    }
    
    /* 计算图像特征 */
    result->brightness = color_calculate_brightness(result->average_color);
    result->saturation = color_calculate_saturation(result->average_color);
    result->contrast = color_calculate_contrast(result->dominant_color, result->average_color);
    
    /* 颜色平滑处理 */
    if (g_color_ctx.config.color_smooth) {
        result->dominant_color = color_smooth_filter(result->dominant_color);
    }
    
    /* 更新统计信息 */
    uint32_t process_time = system_get_tick() - start_time;
    color_extract_update_stats(process_time);
    
    /* 保存最后的颜色 */
    g_color_ctx.last_color = result->dominant_color;
    
    DEBUG_DEBUG("Color extracted: R=%d G=%d B=%d, brightness=%.2f, process_time=%u ms",
               result->dominant_color.red, result->dominant_color.green, result->dominant_color.blue,
               result->brightness, process_time);
    
    DEBUG_EXIT_RET(COLOR_EXTRACT_OK);
    return COLOR_EXTRACT_OK;
}

/**
 * @brief 获取统计信息
 */
int color_extract_get_stats(color_extract_stats_t* stats)
{
    if (stats == NULL) {
        return COLOR_EXTRACT_ERROR_PARAM;
    }
    
    memcpy(stats, &g_color_ctx.stats, sizeof(color_extract_stats_t));
    return COLOR_EXTRACT_OK;
}

/**
 * @brief 重置统计信息
 */
void color_extract_reset_stats(void)
{
    memset(&g_color_ctx.stats, 0, sizeof(color_extract_stats_t));
    g_color_ctx.stats.min_process_time = UINT32_MAX;
    DEBUG_INFO("Color extract stats reset");
}

/* ========== 私有函数实现 ========== */

/**
 * @brief 验证配置参数
 */
static int color_extract_validate_config(const color_extract_config_t* config)
{
    if (config->cluster_count == 0 || config->cluster_count > 10) {
        return COLOR_EXTRACT_ERROR_PARAM;
    }
    
    if (config->sample_rate == 0 || config->sample_rate > 16) {
        return COLOR_EXTRACT_ERROR_PARAM;
    }
    
    if (config->smooth_factor < 0.0f || config->smooth_factor > 1.0f) {
        return COLOR_EXTRACT_ERROR_PARAM;
    }
    
    return COLOR_EXTRACT_OK;
}

/**
 * @brief 采样像素
 */
static uint32_t color_extract_sample_pixels(const uint8_t* data, uint16_t width, uint16_t height,
                                           camera_format_t format, uint8_t sample_rate,
                                           led_rgb_t* samples, uint32_t max_samples)
{
    uint32_t sample_count = 0;
    
    for (uint16_t y = 0; y < height && sample_count < max_samples; y += sample_rate) {
        for (uint16_t x = 0; x < width && sample_count < max_samples; x += sample_rate) {
            led_rgb_t pixel;
            
            /* 根据像素格式提取颜色 */
            switch (format) {
                case CAMERA_FORMAT_RGB565:
                    {
                        uint32_t offset = (y * width + x) * 2;
                        uint16_t rgb565 = (data[offset + 1] << 8) | data[offset];
                        pixel = color_rgb565_to_rgb888(rgb565);
                    }
                    break;
                    
                case CAMERA_FORMAT_RGB888:
                    {
                        uint32_t offset = (y * width + x) * 3;
                        pixel.red = data[offset];
                        pixel.green = data[offset + 1];
                        pixel.blue = data[offset + 2];
                    }
                    break;
                    
                case CAMERA_FORMAT_YUV422:
                    {
                        /* YUV422格式：YUYV */
                        uint32_t offset = (y * width + x) * 2;
                        uint8_t y_val = data[offset];
                        uint8_t u_val = data[offset + 1];
                        uint8_t v_val = data[offset + 3];
                        pixel = color_yuv422_to_rgb888(y_val, u_val, v_val);
                    }
                    break;
                    
                default:
                    continue;
            }
            
            samples[sample_count++] = pixel;
        }
    }
    
    return sample_count;
}

/**
 * @brief 更新统计信息
 */
static void color_extract_update_stats(uint32_t process_time)
{
    g_color_ctx.stats.total_frames++;
    g_color_ctx.stats.processed_frames++;

    /* 更新处理时间统计 */
    g_color_ctx.stats.avg_process_time =
        (g_color_ctx.stats.avg_process_time * (g_color_ctx.stats.processed_frames - 1) + process_time) /
        g_color_ctx.stats.processed_frames;

    if (process_time > g_color_ctx.stats.max_process_time) {
        g_color_ctx.stats.max_process_time = process_time;
    }

    if (process_time < g_color_ctx.stats.min_process_time) {
        g_color_ctx.stats.min_process_time = process_time;
    }
}

/* ========== 颜色算法实现 ========== */

/**
 * @brief 平均颜色算法
 */
led_rgb_t color_algorithm_average(const uint8_t* data, uint16_t width, uint16_t height,
                                  camera_format_t format, uint8_t sample_rate)
{
    uint32_t r_sum = 0, g_sum = 0, b_sum = 0;
    uint32_t pixel_count = 0;

    for (uint16_t y = 0; y < height; y += sample_rate) {
        for (uint16_t x = 0; x < width; x += sample_rate) {
            led_rgb_t pixel;

            /* 根据像素格式提取颜色 */
            switch (format) {
                case CAMERA_FORMAT_RGB565:
                    {
                        uint32_t offset = (y * width + x) * 2;
                        uint16_t rgb565 = (data[offset + 1] << 8) | data[offset];
                        pixel = color_rgb565_to_rgb888(rgb565);
                    }
                    break;

                case CAMERA_FORMAT_RGB888:
                    {
                        uint32_t offset = (y * width + x) * 3;
                        pixel.red = data[offset];
                        pixel.green = data[offset + 1];
                        pixel.blue = data[offset + 2];
                    }
                    break;

                case CAMERA_FORMAT_YUV422:
                    {
                        uint32_t offset = (y * width + x) * 2;
                        uint8_t y_val = data[offset];
                        uint8_t u_val = data[offset + 1];
                        uint8_t v_val = data[offset + 3];
                        pixel = color_yuv422_to_rgb888(y_val, u_val, v_val);
                    }
                    break;

                default:
                    continue;
            }

            r_sum += pixel.red;
            g_sum += pixel.green;
            b_sum += pixel.blue;
            pixel_count++;
        }
    }

    led_rgb_t result = {0};
    if (pixel_count > 0) {
        result.red = r_sum / pixel_count;
        result.green = g_sum / pixel_count;
        result.blue = b_sum / pixel_count;
    }

    return result;
}

/**
 * @brief 主色调算法 (直方图)
 */
led_rgb_t color_algorithm_dominant(const uint8_t* data, uint16_t width, uint16_t height,
                                   camera_format_t format, uint8_t sample_rate)
{
    /* 简化的直方图实现，将RGB空间分为8x8x8=512个区间 */
    #define HIST_BINS 8
    #define HIST_SIZE (HIST_BINS * HIST_BINS * HIST_BINS)

    uint32_t histogram[HIST_SIZE] = {0};
    uint32_t pixel_count = 0;

    /* 统计直方图 */
    for (uint16_t y = 0; y < height; y += sample_rate) {
        for (uint16_t x = 0; x < width; x += sample_rate) {
            led_rgb_t pixel;

            /* 提取像素颜色 (复用平均算法的代码) */
            switch (format) {
                case CAMERA_FORMAT_RGB565:
                    {
                        uint32_t offset = (y * width + x) * 2;
                        uint16_t rgb565 = (data[offset + 1] << 8) | data[offset];
                        pixel = color_rgb565_to_rgb888(rgb565);
                    }
                    break;

                case CAMERA_FORMAT_RGB888:
                    {
                        uint32_t offset = (y * width + x) * 3;
                        pixel.red = data[offset];
                        pixel.green = data[offset + 1];
                        pixel.blue = data[offset + 2];
                    }
                    break;

                case CAMERA_FORMAT_YUV422:
                    {
                        uint32_t offset = (y * width + x) * 2;
                        uint8_t y_val = data[offset];
                        uint8_t u_val = data[offset + 1];
                        uint8_t v_val = data[offset + 3];
                        pixel = color_yuv422_to_rgb888(y_val, u_val, v_val);
                    }
                    break;

                default:
                    continue;
            }

            /* 计算直方图索引 */
            uint8_t r_bin = (pixel.red * HIST_BINS) / 256;
            uint8_t g_bin = (pixel.green * HIST_BINS) / 256;
            uint8_t b_bin = (pixel.blue * HIST_BINS) / 256;

            if (r_bin >= HIST_BINS) r_bin = HIST_BINS - 1;
            if (g_bin >= HIST_BINS) g_bin = HIST_BINS - 1;
            if (b_bin >= HIST_BINS) b_bin = HIST_BINS - 1;

            uint32_t index = r_bin * HIST_BINS * HIST_BINS + g_bin * HIST_BINS + b_bin;
            histogram[index]++;
            pixel_count++;
        }
    }

    /* 找到最大频次的颜色 */
    uint32_t max_count = 0;
    uint32_t max_index = 0;

    for (uint32_t i = 0; i < HIST_SIZE; i++) {
        if (histogram[i] > max_count) {
            max_count = histogram[i];
            max_index = i;
        }
    }

    /* 将索引转换回RGB值 */
    led_rgb_t result = {0};
    if (max_count > 0) {
        uint8_t r_bin = max_index / (HIST_BINS * HIST_BINS);
        uint8_t g_bin = (max_index % (HIST_BINS * HIST_BINS)) / HIST_BINS;
        uint8_t b_bin = max_index % HIST_BINS;

        result.red = (r_bin * 255) / (HIST_BINS - 1);
        result.green = (g_bin * 255) / (HIST_BINS - 1);
        result.blue = (b_bin * 255) / (HIST_BINS - 1);
    }

    return result;
}

/**
 * @brief K-means聚类算法
 */
int color_algorithm_kmeans(const uint8_t* data, uint16_t width, uint16_t height,
                           camera_format_t format, uint8_t cluster_count,
                           uint8_t sample_rate, led_rgb_t* colors)
{
    if (colors == NULL || cluster_count == 0) {
        return 0;
    }

    /* 采样像素 */
    uint32_t max_samples = (width / sample_rate) * (height / sample_rate);
    led_rgb_t* samples = (led_rgb_t*)system_malloc(max_samples * sizeof(led_rgb_t));
    if (samples == NULL) {
        return 0;
    }

    uint32_t sample_count = 0;
    for (uint16_t y = 0; y < height; y += sample_rate) {
        for (uint16_t x = 0; x < width; x += sample_rate) {
            led_rgb_t pixel;

            /* 提取像素颜色 */
            switch (format) {
                case CAMERA_FORMAT_RGB565:
                    {
                        uint32_t offset = (y * width + x) * 2;
                        uint16_t rgb565 = (data[offset + 1] << 8) | data[offset];
                        pixel = color_rgb565_to_rgb888(rgb565);
                    }
                    break;

                case CAMERA_FORMAT_RGB888:
                    {
                        uint32_t offset = (y * width + x) * 3;
                        pixel.red = data[offset];
                        pixel.green = data[offset + 1];
                        pixel.blue = data[offset + 2];
                    }
                    break;

                case CAMERA_FORMAT_YUV422:
                    {
                        uint32_t offset = (y * width + x) * 2;
                        uint8_t y_val = data[offset];
                        uint8_t u_val = data[offset + 1];
                        uint8_t v_val = data[offset + 3];
                        pixel = color_yuv422_to_rgb888(y_val, u_val, v_val);
                    }
                    break;

                default:
                    continue;
            }

            samples[sample_count++] = pixel;
        }
    }

    /* 执行K-means聚类 */
    int result = kmeans_cluster(samples, sample_count, cluster_count, colors);

    system_free(samples);
    return result;
}

/* ========== 颜色空间转换函数 ========== */

/**
 * @brief RGB565转RGB888
 */
led_rgb_t color_rgb565_to_rgb888(uint16_t rgb565)
{
    led_rgb_t result;

    result.red = ((rgb565 >> 11) & 0x1F) * 255 / 31;
    result.green = ((rgb565 >> 5) & 0x3F) * 255 / 63;
    result.blue = (rgb565 & 0x1F) * 255 / 31;

    return result;
}

/**
 * @brief YUV422转RGB888
 */
led_rgb_t color_yuv422_to_rgb888(uint8_t y, uint8_t u, uint8_t v)
{
    led_rgb_t result;

    /* YUV到RGB转换公式 */
    int c = y - 16;
    int d = u - 128;
    int e = v - 128;

    int r = (298 * c + 409 * e + 128) >> 8;
    int g = (298 * c - 100 * d - 208 * e + 128) >> 8;
    int b = (298 * c + 516 * d + 128) >> 8;

    /* 限制范围 */
    result.red = (r < 0) ? 0 : ((r > 255) ? 255 : r);
    result.green = (g < 0) ? 0 : ((g > 255) ? 255 : g);
    result.blue = (b < 0) ? 0 : ((b > 255) ? 255 : b);

    return result;
}

/**
 * @brief RGB转灰度
 */
uint8_t color_rgb_to_gray(led_rgb_t rgb)
{
    /* 使用标准的灰度转换公式 */
    return (uint8_t)(0.299f * rgb.red + 0.587f * rgb.green + 0.114f * rgb.blue);
}

/* ========== 颜色分析工具函数 ========== */

/**
 * @brief 计算颜色亮度
 */
float color_calculate_brightness(led_rgb_t rgb)
{
    /* 使用HSV中的V值作为亮度 */
    uint8_t max_val = rgb.red;
    if (rgb.green > max_val) max_val = rgb.green;
    if (rgb.blue > max_val) max_val = rgb.blue;

    return (float)max_val / 255.0f;
}

/**
 * @brief 计算颜色饱和度
 */
float color_calculate_saturation(led_rgb_t rgb)
{
    uint8_t max_val = rgb.red;
    uint8_t min_val = rgb.red;

    if (rgb.green > max_val) max_val = rgb.green;
    if (rgb.blue > max_val) max_val = rgb.blue;

    if (rgb.green < min_val) min_val = rgb.green;
    if (rgb.blue < min_val) min_val = rgb.blue;

    if (max_val == 0) {
        return 0.0f;
    }

    return (float)(max_val - min_val) / (float)max_val;
}

/**
 * @brief 计算颜色对比度
 */
float color_calculate_contrast(led_rgb_t rgb1, led_rgb_t rgb2)
{
    float brightness1 = color_calculate_brightness(rgb1);
    float brightness2 = color_calculate_brightness(rgb2);

    float diff = fabsf(brightness1 - brightness2);
    return diff;
}

/**
 * @brief 颜色距离计算
 */
float color_calculate_distance(led_rgb_t rgb1, led_rgb_t rgb2)
{
    float dr = (float)(rgb1.red - rgb2.red);
    float dg = (float)(rgb1.green - rgb2.green);
    float db = (float)(rgb1.blue - rgb2.blue);

    return sqrtf(dr * dr + dg * dg + db * db);
}

/* ========== K-means聚类实现 ========== */

/**
 * @brief K-means聚类算法实现
 */
static int kmeans_cluster(const led_rgb_t* samples, uint32_t sample_count,
                         uint8_t cluster_count, led_rgb_t* centers)
{
    if (samples == NULL || centers == NULL || sample_count == 0 || cluster_count == 0) {
        return 0;
    }

    /* 分配聚类中心和分配数组 */
    kmeans_center_t* k_centers = (kmeans_center_t*)system_malloc(cluster_count * sizeof(kmeans_center_t));
    uint8_t* assignments = (uint8_t*)system_malloc(sample_count * sizeof(uint8_t));

    if (k_centers == NULL || assignments == NULL) {
        if (k_centers) system_free(k_centers);
        if (assignments) system_free(assignments);
        return 0;
    }

    /* 初始化聚类中心 (随机选择) */
    for (uint8_t i = 0; i < cluster_count; i++) {
        uint32_t idx = (i * sample_count) / cluster_count;
        k_centers[i].r = samples[idx].red;
        k_centers[i].g = samples[idx].green;
        k_centers[i].b = samples[idx].blue;
        k_centers[i].count = 0;
    }

    /* 迭代优化 */
    const int max_iterations = 10;
    for (int iter = 0; iter < max_iterations; iter++) {
        bool changed = false;

        /* 分配每个样本到最近的聚类中心 */
        for (uint32_t i = 0; i < sample_count; i++) {
            float min_distance = FLT_MAX;
            uint8_t best_cluster = 0;

            for (uint8_t j = 0; j < cluster_count; j++) {
                led_rgb_t center = {
                    .red = (uint8_t)k_centers[j].r,
                    .green = (uint8_t)k_centers[j].g,
                    .blue = (uint8_t)k_centers[j].b
                };

                float distance = color_distance_squared(samples[i], center);
                if (distance < min_distance) {
                    min_distance = distance;
                    best_cluster = j;
                }
            }

            if (assignments[i] != best_cluster) {
                assignments[i] = best_cluster;
                changed = true;
            }
        }

        /* 更新聚类中心 */
        for (uint8_t j = 0; j < cluster_count; j++) {
            k_centers[j].r = 0;
            k_centers[j].g = 0;
            k_centers[j].b = 0;
            k_centers[j].count = 0;
        }

        for (uint32_t i = 0; i < sample_count; i++) {
            uint8_t cluster = assignments[i];
            k_centers[cluster].r += samples[i].red;
            k_centers[cluster].g += samples[i].green;
            k_centers[cluster].b += samples[i].blue;
            k_centers[cluster].count++;
        }

        for (uint8_t j = 0; j < cluster_count; j++) {
            if (k_centers[j].count > 0) {
                k_centers[j].r /= k_centers[j].count;
                k_centers[j].g /= k_centers[j].count;
                k_centers[j].b /= k_centers[j].count;
            }
        }

        if (!changed) {
            break;
        }
    }

    /* 按像素数量排序聚类中心 */
    for (uint8_t i = 0; i < cluster_count - 1; i++) {
        for (uint8_t j = i + 1; j < cluster_count; j++) {
            if (k_centers[j].count > k_centers[i].count) {
                kmeans_center_t temp = k_centers[i];
                k_centers[i] = k_centers[j];
                k_centers[j] = temp;
            }
        }
    }

    /* 输出结果 */
    uint8_t valid_clusters = 0;
    for (uint8_t i = 0; i < cluster_count; i++) {
        if (k_centers[i].count > 0) {
            centers[valid_clusters].red = (uint8_t)k_centers[i].r;
            centers[valid_clusters].green = (uint8_t)k_centers[i].g;
            centers[valid_clusters].blue = (uint8_t)k_centers[i].b;
            valid_clusters++;
        }
    }

    system_free(k_centers);
    system_free(assignments);

    return valid_clusters;
}

/**
 * @brief 计算颜色距离的平方
 */
static float color_distance_squared(led_rgb_t c1, led_rgb_t c2)
{
    float dr = (float)(c1.red - c2.red);
    float dg = (float)(c1.green - c2.green);
    float db = (float)(c1.blue - c2.blue);

    return dr * dr + dg * dg + db * db;
}

/* ========== 颜色平滑滤波器 ========== */

static struct {
    led_rgb_t last_color;
    float smooth_factor;
    bool initialized;
} g_smooth_filter = {0};

/**
 * @brief 颜色平滑滤波器初始化
 */
int color_smooth_init(float factor)
{
    if (factor < 0.0f || factor > 1.0f) {
        return COLOR_EXTRACT_ERROR_PARAM;
    }

    g_smooth_filter.smooth_factor = factor;
    g_smooth_filter.initialized = false;

    return COLOR_EXTRACT_OK;
}

/**
 * @brief 颜色平滑处理
 */
led_rgb_t color_smooth_filter(led_rgb_t current_color)
{
    if (!g_smooth_filter.initialized) {
        g_smooth_filter.last_color = current_color;
        g_smooth_filter.initialized = true;
        return current_color;
    }

    /* 指数移动平均滤波 */
    led_rgb_t result;
    float alpha = g_smooth_filter.smooth_factor;

    result.red = (uint8_t)(alpha * current_color.red + (1.0f - alpha) * g_smooth_filter.last_color.red);
    result.green = (uint8_t)(alpha * current_color.green + (1.0f - alpha) * g_smooth_filter.last_color.green);
    result.blue = (uint8_t)(alpha * current_color.blue + (1.0f - alpha) * g_smooth_filter.last_color.blue);

    g_smooth_filter.last_color = result;

    return result;
}

/**
 * @brief 重置颜色平滑滤波器
 */
void color_smooth_reset(void)
{
    g_smooth_filter.initialized = false;
    memset(&g_smooth_filter.last_color, 0, sizeof(led_rgb_t));
}
