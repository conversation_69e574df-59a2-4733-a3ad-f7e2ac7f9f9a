/**
 * @file sync_engine.h
 * @brief LED同步引擎接口定义
 * <AUTHOR> Sync System Team
 * @date 2025-07-25
 */

#ifndef SYNC_ENGINE_H
#define SYNC_ENGINE_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "system_config.h"
#include "drivers/camera_driver.h"
#include "drivers/led_driver.h"
#include "drivers/ble_driver.h"
#include "algorithm/color_extract.h"

/* ========== 同步引擎数据结构 ========== */

/**
 * @brief 同步模式
 */
typedef enum {
    SYNC_MODE_DISABLED = 0,     /**< 禁用同步 */
    SYNC_MODE_FULL_SCREEN,      /**< 全屏同步 */
    SYNC_MODE_EDGE_ONLY,        /**< 仅边缘同步 */
    SYNC_MODE_REGION_BASED,     /**< 基于区域同步 */
    SYNC_MODE_CUSTOM            /**< 自定义模式 */
} sync_mode_t;

/**
 * @brief LED映射模式
 */
typedef enum {
    LED_MAP_LINEAR = 0,         /**< 线性映射 */
    LED_MAP_CIRCULAR,           /**< 环形映射 */
    LED_MAP_MATRIX,             /**< 矩阵映射 */
    LED_MAP_CUSTOM              /**< 自定义映射 */
} led_mapping_t;

/**
 * @brief 同步引擎配置
 */
typedef struct {
    sync_mode_t sync_mode;              /**< 同步模式 */
    led_mapping_t led_mapping;          /**< LED映射模式 */
    uint8_t led_count;                  /**< LED数量 */
    uint8_t brightness;                 /**< 亮度 */
    uint8_t saturation_boost;           /**< 饱和度增强 */
    uint8_t color_temperature;          /**< 色温调节 */
    bool auto_brightness;               /**< 自动亮度 */
    bool smooth_transition;             /**< 平滑过渡 */
    uint16_t transition_time;           /**< 过渡时间 (ms) */
    uint8_t update_rate;                /**< 更新频率 (Hz) */
    color_extract_config_t color_config; /**< 颜色提取配置 */
} sync_engine_config_t;

/**
 * @brief 同步引擎状态
 */
typedef enum {
    SYNC_STATE_UNINIT = 0,      /**< 未初始化 */
    SYNC_STATE_INIT,            /**< 已初始化 */
    SYNC_STATE_RUNNING,         /**< 运行中 */
    SYNC_STATE_PAUSED,          /**< 暂停 */
    SYNC_STATE_ERROR            /**< 错误状态 */
} sync_engine_state_t;

/**
 * @brief 同步引擎统计
 */
typedef struct {
    uint32_t total_frames;              /**< 总帧数 */
    uint32_t processed_frames;          /**< 已处理帧数 */
    uint32_t dropped_frames;            /**< 丢帧数 */
    float current_fps;                  /**< 当前FPS */
    uint32_t avg_process_time;          /**< 平均处理时间 (us) */
    uint32_t max_process_time;          /**< 最大处理时间 (us) */
    uint32_t led_updates;               /**< LED更新次数 */
    uint32_t color_changes;             /**< 颜色变化次数 */
} sync_engine_stats_t;

/**
 * @brief LED区域映射
 */
typedef struct {
    uint16_t led_start;         /**< LED起始索引 */
    uint16_t led_count;         /**< LED数量 */
    uint16_t screen_x;          /**< 屏幕区域X */
    uint16_t screen_y;          /**< 屏幕区域Y */
    uint16_t screen_width;      /**< 屏幕区域宽度 */
    uint16_t screen_height;     /**< 屏幕区域高度 */
    float weight;               /**< 权重 */
} led_region_map_t;

/* ========== 同步引擎接口 ========== */

/**
 * @brief 同步引擎初始化
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_init(void);

/**
 * @brief 同步引擎去初始化
 */
void sync_engine_deinit(void);

/**
 * @brief 启动同步引擎
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_start(void);

/**
 * @brief 停止同步引擎
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_stop(void);

/**
 * @brief 暂停同步引擎
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_pause(void);

/**
 * @brief 恢复同步引擎
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_resume(void);

/**
 * @brief 处理摄像头帧数据
 * @param frame 摄像头帧数据
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_process_frame(const camera_frame_t* frame);

/**
 * @brief 获取同步引擎状态
 * @return 同步引擎状态
 */
sync_engine_state_t sync_engine_get_state(void);

/**
 * @brief 检查同步引擎是否运行
 * @return true: 运行中, false: 未运行
 */
bool sync_engine_is_running(void);

/* ========== 配置管理接口 ========== */

/**
 * @brief 设置同步引擎配置
 * @param config 配置参数
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_set_config(const sync_engine_config_t* config);

/**
 * @brief 获取同步引擎配置
 * @param config 配置参数输出
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_get_config(sync_engine_config_t* config);

/**
 * @brief 加载默认配置
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_load_default_config(void);

/**
 * @brief 保存配置到Flash
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_save_config(void);

/**
 * @brief 从Flash加载配置
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_load_config(void);

/* ========== 同步模式控制接口 ========== */

/**
 * @brief 设置同步模式
 * @param mode 同步模式
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_set_sync_mode(sync_mode_t mode);

/**
 * @brief 获取同步模式
 * @return 当前同步模式
 */
sync_mode_t sync_engine_get_sync_mode(void);

/**
 * @brief 设置LED映射模式
 * @param mapping LED映射模式
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_set_led_mapping(led_mapping_t mapping);

/**
 * @brief 设置自定义LED区域映射
 * @param maps 区域映射数组
 * @param count 映射数量
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_set_custom_mapping(const led_region_map_t* maps, uint8_t count);

/* ========== 参数调节接口 ========== */

/**
 * @brief 设置亮度
 * @param brightness 亮度值 (0-255)
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_set_brightness(uint8_t brightness);

/**
 * @brief 获取亮度
 * @return 当前亮度值
 */
uint8_t sync_engine_get_brightness(void);

/**
 * @brief 设置饱和度增强
 * @param boost 饱和度增强值 (0-255)
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_set_saturation_boost(uint8_t boost);

/**
 * @brief 设置色温
 * @param temperature 色温值 (0-255)
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_set_color_temperature(uint8_t temperature);

/**
 * @brief 设置自动亮度
 * @param enable true: 使能, false: 禁用
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_set_auto_brightness(bool enable);

/**
 * @brief 设置平滑过渡
 * @param enable true: 使能, false: 禁用
 * @param transition_time 过渡时间 (ms)
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_set_smooth_transition(bool enable, uint16_t transition_time);

/* ========== 统计信息接口 ========== */

/**
 * @brief 获取统计信息
 * @param stats 统计信息输出
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_get_stats(sync_engine_stats_t* stats);

/**
 * @brief 重置统计信息
 */
void sync_engine_reset_stats(void);

/**
 * @brief 获取性能信息
 * @param cpu_usage CPU使用率输出 (0-100)
 * @param memory_usage 内存使用率输出 (0-100)
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_get_performance(uint8_t* cpu_usage, uint8_t* memory_usage);

/* ========== BLE命令处理接口 ========== */

/**
 * @brief 处理BLE命令
 * @param packet BLE数据包
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_handle_ble_command(const ble_packet_t* packet);

/**
 * @brief 发送状态更新
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_send_status_update(void);

/* ========== 调试和测试接口 ========== */

/**
 * @brief 设置测试颜色
 * @param color 测试颜色
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_set_test_color(led_rgb_t color);

/**
 * @brief 启动颜色测试序列
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_start_color_test(void);

/**
 * @brief 启动性能测试
 * @param duration 测试持续时间 (ms)
 * @return 0: 成功, 负数: 错误码
 */
int sync_engine_start_performance_test(uint32_t duration);

/* ========== 错误码定义 ========== */
#define SYNC_ENGINE_OK                  0
#define SYNC_ENGINE_ERROR              -1
#define SYNC_ENGINE_ERROR_PARAM        -2
#define SYNC_ENGINE_ERROR_MEMORY       -3
#define SYNC_ENGINE_ERROR_TIMEOUT      -4
#define SYNC_ENGINE_ERROR_BUSY         -5
#define SYNC_ENGINE_ERROR_NOT_READY    -6
#define SYNC_ENGINE_ERROR_NOT_SUPPORT  -7
#define SYNC_ENGINE_ERROR_CONFIG       -8

#ifdef __cplusplus
}
#endif

#endif /* SYNC_ENGINE_H */
