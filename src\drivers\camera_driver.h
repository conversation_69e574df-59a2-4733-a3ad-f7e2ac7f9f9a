/**
 * @file camera_driver.h
 * @brief 摄像头驱动接口定义
 * <AUTHOR> Sync System Team
 * @date 2025-07-25
 */

#ifndef CAMERA_DRIVER_H
#define CAMERA_DRIVER_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "system_config.h"

/* ========== 摄像头数据结构 ========== */

/**
 * @brief 摄像头帧数据结构
 */
typedef struct {
    uint16_t width;          /**< 图像宽度 */
    uint16_t height;         /**< 图像高度 */
    uint32_t size;           /**< 数据大小 */
    uint8_t* data;           /**< 图像数据指针 */
    uint32_t timestamp;      /**< 时间戳 */
    uint32_t frame_id;       /**< 帧ID */
    camera_format_t format;  /**< 像素格式 */
} camera_frame_t;

/**
 * @brief 摄像头配置结构
 */
typedef struct {
    uint16_t width;                 /**< 图像宽度 */
    uint16_t height;                /**< 图像高度 */
    uint8_t fps;                    /**< 帧率 */
    camera_format_t format;         /**< 像素格式 */
    camera_interface_t interface;   /**< 接口类型 */
    uint8_t brightness;             /**< 亮度 (0-255) */
    uint8_t contrast;               /**< 对比度 (0-255) */
    uint8_t saturation;             /**< 饱和度 (0-255) */
    bool auto_exposure;             /**< 自动曝光 */
    bool auto_white_balance;        /**< 自动白平衡 */
} camera_config_t;

/**
 * @brief 摄像头状态
 */
typedef enum {
    CAMERA_STATE_UNINIT = 0,    /**< 未初始化 */
    CAMERA_STATE_INIT,          /**< 已初始化 */
    CAMERA_STATE_STREAMING,     /**< 正在采集 */
    CAMERA_STATE_ERROR          /**< 错误状态 */
} camera_state_t;

/**
 * @brief 摄像头统计信息
 */
typedef struct {
    uint32_t total_frames;      /**< 总帧数 */
    uint32_t dropped_frames;    /**< 丢帧数 */
    uint32_t error_frames;      /**< 错误帧数 */
    float current_fps;          /**< 当前帧率 */
    uint32_t last_frame_time;   /**< 最后一帧时间 */
} camera_stats_t;

/* ========== 摄像头驱动接口 ========== */

/**
 * @brief 摄像头初始化
 * @return 0: 成功, 负数: 错误码
 */
int camera_init(void);

/**
 * @brief 摄像头去初始化
 */
void camera_deinit(void);

/**
 * @brief 开始视频流采集
 * @return 0: 成功, 负数: 错误码
 */
int camera_start_streaming(void);

/**
 * @brief 停止视频流采集
 * @return 0: 成功, 负数: 错误码
 */
int camera_stop_streaming(void);

/**
 * @brief 检查是否有新帧可用
 * @return true: 有新帧, false: 无新帧
 */
bool camera_is_frame_ready(void);

/**
 * @brief 获取最新帧数据
 * @return 帧数据指针，无数据时返回NULL
 */
camera_frame_t* camera_get_frame(void);

/**
 * @brief 释放帧数据
 * @param frame 帧数据指针
 */
void camera_release_frame(camera_frame_t* frame);

/**
 * @brief 设置摄像头配置
 * @param config 配置参数
 * @return 0: 成功, 负数: 错误码
 */
int camera_set_config(const camera_config_t* config);

/**
 * @brief 获取摄像头配置
 * @param config 配置参数输出
 * @return 0: 成功, 负数: 错误码
 */
int camera_get_config(camera_config_t* config);

/**
 * @brief 获取摄像头状态
 * @return 摄像头状态
 */
camera_state_t camera_get_state(void);

/**
 * @brief 获取摄像头统计信息
 * @param stats 统计信息输出
 * @return 0: 成功, 负数: 错误码
 */
int camera_get_stats(camera_stats_t* stats);

/**
 * @brief 重置摄像头统计信息
 */
void camera_reset_stats(void);

/* ========== 摄像头控制接口 ========== */

/**
 * @brief 设置亮度
 * @param brightness 亮度值 (0-255)
 * @return 0: 成功, 负数: 错误码
 */
int camera_set_brightness(uint8_t brightness);

/**
 * @brief 设置对比度
 * @param contrast 对比度值 (0-255)
 * @return 0: 成功, 负数: 错误码
 */
int camera_set_contrast(uint8_t contrast);

/**
 * @brief 设置饱和度
 * @param saturation 饱和度值 (0-255)
 * @return 0: 成功, 负数: 错误码
 */
int camera_set_saturation(uint8_t saturation);

/**
 * @brief 设置自动曝光
 * @param enable true: 使能, false: 禁用
 * @return 0: 成功, 负数: 错误码
 */
int camera_set_auto_exposure(bool enable);

/**
 * @brief 设置自动白平衡
 * @param enable true: 使能, false: 禁用
 * @return 0: 成功, 负数: 错误码
 */
int camera_set_auto_white_balance(bool enable);

/* ========== 错误码定义 ========== */
#define CAMERA_OK                   0
#define CAMERA_ERROR               -1
#define CAMERA_ERROR_PARAM         -2
#define CAMERA_ERROR_MEMORY        -3
#define CAMERA_ERROR_TIMEOUT       -4
#define CAMERA_ERROR_BUSY          -5
#define CAMERA_ERROR_NOT_READY     -6
#define CAMERA_ERROR_NOT_SUPPORT   -7
#define CAMERA_ERROR_NO_DEVICE     -8
#define CAMERA_ERROR_IO            -9

#ifdef __cplusplus
}
#endif

#endif /* CAMERA_DRIVER_H */
