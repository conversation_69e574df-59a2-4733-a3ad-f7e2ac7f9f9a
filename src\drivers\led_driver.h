/**
 * @file led_driver.h
 * @brief LED驱动接口定义
 * <AUTHOR> Sync System Team
 * @date 2025-07-25
 */

#ifndef LED_DRIVER_H
#define LED_DRIVER_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "system_config.h"

/* ========== LED数据结构 ========== */

/**
 * @brief RGB颜色结构
 */
typedef struct {
    uint8_t red;        /**< 红色分量 (0-255) */
    uint8_t green;      /**< 绿色分量 (0-255) */
    uint8_t blue;       /**< 蓝色分量 (0-255) */
} led_rgb_t;

/**
 * @brief RGBW颜色结构
 */
typedef struct {
    uint8_t red;        /**< 红色分量 (0-255) */
    uint8_t green;      /**< 绿色分量 (0-255) */
    uint8_t blue;       /**< 蓝色分量 (0-255) */
    uint8_t white;      /**< 白色分量 (0-255) */
} led_rgbw_t;

/**
 * @brief HSV颜色结构
 */
typedef struct {
    uint16_t hue;       /**< 色调 (0-359) */
    uint8_t saturation; /**< 饱和度 (0-255) */
    uint8_t value;      /**< 明度 (0-255) */
} led_hsv_t;

/**
 * @brief LED配置结构
 */
typedef struct {
    uint16_t led_count;         /**< LED数量 */
    led_type_t led_type;        /**< LED类型 */
    uint8_t brightness;         /**< 全局亮度 (0-255) */
    bool gamma_correction;      /**< 伽马校正使能 */
    uint8_t color_order;        /**< 颜色顺序 (RGB/GRB/BGR等) */
    uint32_t update_rate;       /**< 更新频率 (Hz) */
} led_config_t;

/**
 * @brief LED效果类型
 */
typedef enum {
    LED_EFFECT_NONE = 0,        /**< 无效果 */
    LED_EFFECT_STATIC,          /**< 静态颜色 */
    LED_EFFECT_BREATHING,       /**< 呼吸灯 */
    LED_EFFECT_RAINBOW,         /**< 彩虹效果 */
    LED_EFFECT_CHASE,           /**< 追逐效果 */
    LED_EFFECT_FADE,            /**< 渐变效果 */
    LED_EFFECT_STROBE,          /**< 闪烁效果 */
    LED_EFFECT_CUSTOM           /**< 自定义效果 */
} led_effect_t;

/**
 * @brief LED效果参数
 */
typedef struct {
    led_effect_t type;          /**< 效果类型 */
    uint32_t duration;          /**< 持续时间 (ms) */
    uint32_t period;            /**< 周期时间 (ms) */
    led_rgb_t color1;           /**< 主颜色 */
    led_rgb_t color2;           /**< 辅助颜色 */
    uint8_t speed;              /**< 速度 (0-255) */
    bool reverse;               /**< 反向播放 */
} led_effect_params_t;

/**
 * @brief LED状态
 */
typedef enum {
    LED_STATE_UNINIT = 0,       /**< 未初始化 */
    LED_STATE_INIT,             /**< 已初始化 */
    LED_STATE_RUNNING,          /**< 运行中 */
    LED_STATE_PAUSED,           /**< 暂停 */
    LED_STATE_ERROR             /**< 错误状态 */
} led_state_t;

/* ========== LED驱动接口 ========== */

/**
 * @brief LED驱动初始化
 * @return 0: 成功, 负数: 错误码
 */
int led_init(void);

/**
 * @brief LED驱动去初始化
 */
void led_deinit(void);

/**
 * @brief 设置LED配置
 * @param config 配置参数
 * @return 0: 成功, 负数: 错误码
 */
int led_set_config(const led_config_t* config);

/**
 * @brief 获取LED配置
 * @param config 配置参数输出
 * @return 0: 成功, 负数: 错误码
 */
int led_get_config(led_config_t* config);

/**
 * @brief 更新LED显示
 * @return 0: 成功, 负数: 错误码
 */
int led_update(void);

/**
 * @brief 获取LED状态
 * @return LED状态
 */
led_state_t led_get_state(void);

/* ========== LED颜色控制接口 ========== */

/**
 * @brief 设置单个LED颜色
 * @param index LED索引
 * @param color RGB颜色
 * @return 0: 成功, 负数: 错误码
 */
int led_set_pixel(uint16_t index, led_rgb_t color);

/**
 * @brief 获取单个LED颜色
 * @param index LED索引
 * @param color RGB颜色输出
 * @return 0: 成功, 负数: 错误码
 */
int led_get_pixel(uint16_t index, led_rgb_t* color);

/**
 * @brief 设置所有LED颜色
 * @param color RGB颜色
 * @return 0: 成功, 负数: 错误码
 */
int led_set_all(led_rgb_t color);

/**
 * @brief 设置LED范围颜色
 * @param start 起始索引
 * @param count LED数量
 * @param color RGB颜色
 * @return 0: 成功, 负数: 错误码
 */
int led_set_range(uint16_t start, uint16_t count, led_rgb_t color);

/**
 * @brief 批量设置LED颜色
 * @param colors RGB颜色数组
 * @param count 数组长度
 * @return 0: 成功, 负数: 错误码
 */
int led_set_buffer(const led_rgb_t* colors, uint16_t count);

/**
 * @brief 清除所有LED (设为黑色)
 * @return 0: 成功, 负数: 错误码
 */
int led_clear(void);

/* ========== LED亮度控制接口 ========== */

/**
 * @brief 设置全局亮度
 * @param brightness 亮度值 (0-255)
 * @return 0: 成功, 负数: 错误码
 */
int led_set_brightness(uint8_t brightness);

/**
 * @brief 获取全局亮度
 * @return 亮度值 (0-255)
 */
uint8_t led_get_brightness(void);

/**
 * @brief 渐变到指定亮度
 * @param target_brightness 目标亮度 (0-255)
 * @param duration 渐变时间 (ms)
 * @return 0: 成功, 负数: 错误码
 */
int led_fade_brightness(uint8_t target_brightness, uint32_t duration);

/* ========== LED效果控制接口 ========== */

/**
 * @brief 启动LED效果
 * @param params 效果参数
 * @return 0: 成功, 负数: 错误码
 */
int led_start_effect(const led_effect_params_t* params);

/**
 * @brief 停止LED效果
 * @return 0: 成功, 负数: 错误码
 */
int led_stop_effect(void);

/**
 * @brief 暂停LED效果
 * @return 0: 成功, 负数: 错误码
 */
int led_pause_effect(void);

/**
 * @brief 恢复LED效果
 * @return 0: 成功, 负数: 错误码
 */
int led_resume_effect(void);

/**
 * @brief 检查效果是否运行中
 * @return true: 运行中, false: 已停止
 */
bool led_is_effect_running(void);

/* ========== LED测试接口 ========== */

/**
 * @brief LED测试序列
 * @return 0: 成功, 负数: 错误码
 */
int led_test_sequence(void);

/**
 * @brief RGB测试
 * @return 0: 成功, 负数: 错误码
 */
int led_test_rgb(void);

/**
 * @brief 亮度测试
 * @return 0: 成功, 负数: 错误码
 */
int led_test_brightness(void);

/* ========== 颜色转换工具函数 ========== */

/**
 * @brief HSV转RGB
 * @param hsv HSV颜色
 * @return RGB颜色
 */
led_rgb_t led_hsv_to_rgb(led_hsv_t hsv);

/**
 * @brief RGB转HSV
 * @param rgb RGB颜色
 * @return HSV颜色
 */
led_hsv_t led_rgb_to_hsv(led_rgb_t rgb);

/**
 * @brief 伽马校正
 * @param color 原始颜色
 * @return 校正后颜色
 */
led_rgb_t led_gamma_correct(led_rgb_t color);

/* ========== 错误码定义 ========== */
#define LED_OK                  0
#define LED_ERROR              -1
#define LED_ERROR_PARAM        -2
#define LED_ERROR_MEMORY       -3
#define LED_ERROR_TIMEOUT      -4
#define LED_ERROR_BUSY         -5
#define LED_ERROR_NOT_READY    -6
#define LED_ERROR_NOT_SUPPORT  -7
#define LED_ERROR_INDEX        -8

#ifdef __cplusplus
}
#endif

#endif /* LED_DRIVER_H */
