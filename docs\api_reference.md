# LED背景灯同步系统 - API参考文档

## 文档信息

- **项目名称**: LED背景灯同步系统 API参考
- **版本**: v1.0.0
- **日期**: 2025-07-25

## 1. 系统初始化API

### 1.1 system_init.h

#### system_init()
```c
int system_init(void);
```
**功能**: 系统基础初始化  
**返回值**: 0成功，负数为错误码  
**说明**: 初始化时钟、GPIO、定时器等基础硬件

#### system_get_tick()
```c
uint32_t system_get_tick(void);
```
**功能**: 获取系统运行时间  
**返回值**: 系统运行时间(ms)  
**说明**: 用于时间戳和延时计算

#### system_delay_ms()
```c
void system_delay_ms(uint32_t ms);
```
**功能**: 毫秒级延时  
**参数**: ms - 延时时间(毫秒)  
**说明**: 阻塞式延时函数

## 2. 摄像头驱动API

### 2.1 camera_driver.h

#### camera_init()
```c
int camera_init(void);
```
**功能**: 摄像头初始化  
**返回值**: CAMERA_OK成功，负数为错误码  
**说明**: 初始化摄像头硬件和缓冲区

#### camera_start_streaming()
```c
int camera_start_streaming(void);
```
**功能**: 开始视频流采集  
**返回值**: CAMERA_OK成功，负数为错误码  
**说明**: 启动摄像头数据采集

#### camera_get_frame()
```c
camera_frame_t* camera_get_frame(void);
```
**功能**: 获取最新帧数据  
**返回值**: 帧数据指针，NULL表示无数据  
**说明**: 非阻塞获取，需要配合camera_release_frame使用

#### camera_release_frame()
```c
void camera_release_frame(camera_frame_t* frame);
```
**功能**: 释放帧数据  
**参数**: frame - 帧数据指针  
**说明**: 释放帧缓冲区，允许重新使用

#### camera_set_config()
```c
int camera_set_config(const camera_config_t* config);
```
**功能**: 设置摄像头配置  
**参数**: config - 配置参数结构体指针  
**返回值**: CAMERA_OK成功，负数为错误码  
**说明**: 动态修改摄像头参数

## 3. LED驱动API

### 3.1 led_driver.h

#### led_init()
```c
int led_init(void);
```
**功能**: LED驱动初始化  
**返回值**: LED_OK成功，负数为错误码  
**说明**: 初始化SPI接口和LED缓冲区

#### led_set_pixel()
```c
int led_set_pixel(uint16_t index, led_rgb_t color);
```
**功能**: 设置单个LED颜色  
**参数**: 
- index - LED索引(0开始)
- color - RGB颜色值
**返回值**: LED_OK成功，负数为错误码  
**说明**: 设置指定位置LED的颜色

#### led_set_all()
```c
int led_set_all(led_rgb_t color);
```
**功能**: 设置所有LED颜色  
**参数**: color - RGB颜色值  
**返回值**: LED_OK成功，负数为错误码  
**说明**: 将所有LED设置为相同颜色

#### led_update()
```c
int led_update(void);
```
**功能**: 更新LED显示  
**返回值**: LED_OK成功，负数为错误码  
**说明**: 将缓冲区数据发送到LED硬件

#### led_set_brightness()
```c
int led_set_brightness(uint8_t brightness);
```
**功能**: 设置全局亮度  
**参数**: brightness - 亮度值(0-255)  
**返回值**: LED_OK成功，负数为错误码  
**说明**: 调整所有LED的亮度

#### led_start_effect()
```c
int led_start_effect(const led_effect_params_t* params);
```
**功能**: 启动LED效果  
**参数**: params - 效果参数结构体指针  
**返回值**: LED_OK成功，负数为错误码  
**说明**: 启动预定义的LED动画效果

## 4. 蓝牙通信API

### 4.1 ble_driver.h

#### ble_init()
```c
int ble_init(void);
```
**功能**: BLE驱动初始化  
**返回值**: BLE_OK成功，负数为错误码  
**说明**: 初始化蓝牙硬件和协议栈

#### ble_start_advertising()
```c
int ble_start_advertising(void);
```
**功能**: 开始BLE广播  
**返回值**: BLE_OK成功，负数为错误码  
**说明**: 启动设备发现广播

#### ble_send_packet()
```c
int ble_send_packet(const ble_packet_t* packet);
```
**功能**: 发送数据包  
**参数**: packet - 数据包结构体指针  
**返回值**: BLE_OK成功，负数为错误码  
**说明**: 发送格式化的数据包

#### ble_set_event_callback()
```c
void ble_set_event_callback(ble_event_callback_t callback);
```
**功能**: 设置事件回调函数  
**参数**: callback - 回调函数指针  
**说明**: 注册BLE事件处理函数

#### ble_is_connected()
```c
bool ble_is_connected(void);
```
**功能**: 检查连接状态  
**返回值**: true已连接，false未连接  
**说明**: 查询当前BLE连接状态

## 5. 颜色提取算法API

### 5.1 color_extract.h

#### color_extract_init()
```c
int color_extract_init(void);
```
**功能**: 颜色提取算法初始化  
**返回值**: COLOR_EXTRACT_OK成功，负数为错误码  
**说明**: 初始化算法缓冲区和参数

#### color_extract_from_frame()
```c
int color_extract_from_frame(const camera_frame_t* frame, 
                             color_extract_result_t* result);
```
**功能**: 从图像帧提取颜色  
**参数**: 
- frame - 图像帧数据
- result - 提取结果输出
**返回值**: COLOR_EXTRACT_OK成功，负数为错误码  
**说明**: 分析图像并提取主要颜色信息

#### color_algorithm_average()
```c
led_rgb_t color_algorithm_average(const uint8_t* data, uint16_t width, 
                                  uint16_t height, camera_format_t format, 
                                  uint8_t sample_rate);
```
**功能**: 平均颜色算法  
**参数**: 
- data - 图像数据
- width - 图像宽度
- height - 图像高度
- format - 像素格式
- sample_rate - 采样率
**返回值**: 平均RGB颜色  
**说明**: 计算图像的平均颜色

#### color_algorithm_kmeans()
```c
int color_algorithm_kmeans(const uint8_t* data, uint16_t width, uint16_t height,
                           camera_format_t format, uint8_t cluster_count, 
                           uint8_t sample_rate, led_rgb_t* colors);
```
**功能**: K-means聚类算法  
**参数**: 
- data - 图像数据
- width - 图像宽度
- height - 图像高度
- format - 像素格式
- cluster_count - 聚类数量
- sample_rate - 采样率
- colors - 输出颜色数组
**返回值**: 有效聚类数量  
**说明**: 使用K-means算法提取主要颜色

#### color_smooth_filter()
```c
led_rgb_t color_smooth_filter(led_rgb_t current_color);
```
**功能**: 颜色平滑滤波  
**参数**: current_color - 当前颜色  
**返回值**: 平滑后的颜色  
**说明**: 对颜色进行时间域平滑处理

## 6. 同步引擎API

### 6.1 sync_engine.h

#### sync_engine_init()
```c
int sync_engine_init(void);
```
**功能**: 同步引擎初始化  
**返回值**: SYNC_ENGINE_OK成功，负数为错误码  
**说明**: 初始化同步引擎和相关模块

#### sync_engine_start()
```c
int sync_engine_start(void);
```
**功能**: 启动同步引擎  
**返回值**: SYNC_ENGINE_OK成功，负数为错误码  
**说明**: 开始颜色同步处理

#### sync_engine_process_frame()
```c
int sync_engine_process_frame(const camera_frame_t* frame);
```
**功能**: 处理摄像头帧数据  
**参数**: frame - 摄像头帧数据  
**返回值**: SYNC_ENGINE_OK成功，负数为错误码  
**说明**: 核心处理函数，实现颜色提取和LED控制

#### sync_engine_set_config()
```c
int sync_engine_set_config(const sync_engine_config_t* config);
```
**功能**: 设置同步引擎配置  
**参数**: config - 配置参数结构体  
**返回值**: SYNC_ENGINE_OK成功，负数为错误码  
**说明**: 动态修改同步参数

#### sync_engine_set_brightness()
```c
int sync_engine_set_brightness(uint8_t brightness);
```
**功能**: 设置亮度  
**参数**: brightness - 亮度值(0-255)  
**返回值**: SYNC_ENGINE_OK成功，负数为错误码  
**说明**: 调整LED整体亮度

#### sync_engine_handle_ble_command()
```c
int sync_engine_handle_ble_command(const ble_packet_t* packet);
```
**功能**: 处理BLE命令  
**参数**: packet - BLE数据包  
**返回值**: SYNC_ENGINE_OK成功，负数为错误码  
**说明**: 处理来自手机APP的控制命令

## 7. 数据结构定义

### 7.1 基础数据类型

#### led_rgb_t
```c
typedef struct {
    uint8_t red;        // 红色分量 (0-255)
    uint8_t green;      // 绿色分量 (0-255)
    uint8_t blue;       // 蓝色分量 (0-255)
} led_rgb_t;
```

#### camera_frame_t
```c
typedef struct {
    uint16_t width;          // 图像宽度
    uint16_t height;         // 图像高度
    uint32_t size;           // 数据大小
    uint8_t* data;           // 图像数据指针
    uint32_t timestamp;      // 时间戳
    uint32_t frame_id;       // 帧ID
    camera_format_t format;  // 像素格式
} camera_frame_t;
```

#### ble_packet_t
```c
typedef struct {
    uint8_t command;            // 命令类型
    uint8_t length;             // 数据长度
    uint8_t data[32];           // 数据内容
    uint8_t checksum;           // 校验和
} ble_packet_t;
```

### 7.2 配置结构体

#### sync_engine_config_t
```c
typedef struct {
    sync_mode_t sync_mode;              // 同步模式
    led_mapping_t led_mapping;          // LED映射模式
    uint8_t led_count;                  // LED数量
    uint8_t brightness;                 // 亮度
    uint8_t saturation_boost;           // 饱和度增强
    uint8_t color_temperature;          // 色温调节
    bool auto_brightness;               // 自动亮度
    bool smooth_transition;             // 平滑过渡
    uint16_t transition_time;           // 过渡时间 (ms)
    uint8_t update_rate;                // 更新频率 (Hz)
    color_extract_config_t color_config; // 颜色提取配置
} sync_engine_config_t;
```

## 8. 错误码参考

### 8.1 通用错误码
- `SYSTEM_OK (0)`: 操作成功
- `SYSTEM_ERROR (-1)`: 通用错误
- `SYSTEM_ERROR_PARAM (-2)`: 参数错误
- `SYSTEM_ERROR_MEMORY (-3)`: 内存不足
- `SYSTEM_ERROR_TIMEOUT (-4)`: 操作超时

### 8.2 模块特定错误码
- `CAMERA_ERROR_NO_DEVICE (-8)`: 摄像头设备未找到
- `LED_ERROR_INDEX (-8)`: LED索引超出范围
- `BLE_ERROR_NOT_CONNECTED (-8)`: BLE未连接
- `COLOR_EXTRACT_ERROR_FORMAT (-6)`: 不支持的图像格式

## 9. 使用示例

### 9.1 基本初始化
```c
// 系统初始化
system_init();

// 初始化各模块
camera_init();
led_init();
ble_init();
color_extract_init();
sync_engine_init();

// 启动同步引擎
sync_engine_start();
```

### 9.2 手动颜色控制
```c
// 设置所有LED为红色
led_rgb_t red = {255, 0, 0};
led_set_all(red);
led_update();

// 设置亮度为50%
led_set_brightness(128);
```

### 9.3 BLE命令处理
```c
// 设置BLE事件回调
ble_set_event_callback(my_ble_callback);

// 在回调函数中处理命令
void my_ble_callback(const ble_event_t* event) {
    if (event->type == BLE_EVENT_DATA_RECEIVED) {
        ble_packet_t* packet = (ble_packet_t*)event->data;
        sync_engine_handle_ble_command(packet);
    }
}
```

---

**文档版本**: v1.0.0  
**最后更新**: 2025-07-25  
**维护者**: LED Sync System Team
