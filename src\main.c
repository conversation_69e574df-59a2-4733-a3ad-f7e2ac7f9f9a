/**
 * @file main.c
 * @brief LED背景灯同步系统主程序
 * <AUTHOR> Sync System Team
 * @date 2025-07-25
 */

#include "system_config.h"
#include "common/system_init.h"
#include "common/debug.h"
#include "drivers/camera_driver.h"
#include "drivers/led_driver.h"
#include "drivers/ble_driver.h"
#include "algorithm/color_extract.h"
#include "application/sync_engine.h"

/* 全局变量 */
static volatile bool system_running = true;
static uint32_t frame_counter = 0;
static uint32_t last_fps_time = 0;

/**
 * @brief 系统初始化
 * @return 0: 成功, -1: 失败
 */
static int system_initialize(void)
{
    int ret = 0;
    
    DEBUG_INFO("LED Sync System v%d.%d.%d starting...", 
               SYSTEM_VERSION_MAJOR, SYSTEM_VERSION_MINOR, SYSTEM_VERSION_PATCH);
    
    /* 基础系统初始化 */
    ret = system_init();
    if (ret != 0) {
        DEBUG_ERROR("System init failed: %d", ret);
        return ret;
    }
    
    /* 摄像头初始化 */
#if CAMERA_ENABLED
    ret = camera_init();
    if (ret != 0) {
        DEBUG_ERROR("Camera init failed: %d", ret);
        return ret;
    }
    DEBUG_INFO("Camera initialized: %dx%d@%dfps", 
               CAMERA_WIDTH, CAMERA_HEIGHT, CAMERA_FPS);
#endif
    
    /* LED驱动初始化 */
#if LED_ENABLED
    ret = led_init();
    if (ret != 0) {
        DEBUG_ERROR("LED init failed: %d", ret);
        return ret;
    }
    DEBUG_INFO("LED driver initialized: %d LEDs", LED_COUNT_DEFAULT);
#endif
    
    /* 蓝牙初始化 */
#if BLE_ENABLED
    ret = ble_init();
    if (ret != 0) {
        DEBUG_ERROR("BLE init failed: %d", ret);
        return ret;
    }
    DEBUG_INFO("BLE initialized: %s", BLE_DEVICE_NAME);
#endif
    
    /* 颜色算法初始化 */
#if ALGORITHM_ENABLED
    ret = color_extract_init();
    if (ret != 0) {
        DEBUG_ERROR("Color algorithm init failed: %d", ret);
        return ret;
    }
    DEBUG_INFO("Color algorithm initialized");
#endif
    
    /* 同步引擎初始化 */
    ret = sync_engine_init();
    if (ret != 0) {
        DEBUG_ERROR("Sync engine init failed: %d", ret);
        return ret;
    }
    DEBUG_INFO("Sync engine initialized");
    
    DEBUG_INFO("System initialization completed successfully");
    return 0;
}

/**
 * @brief 系统去初始化
 */
static void system_deinitialize(void)
{
    DEBUG_INFO("System shutting down...");
    
    sync_engine_deinit();
    
#if ALGORITHM_ENABLED
    color_extract_deinit();
#endif

#if BLE_ENABLED
    ble_deinit();
#endif

#if LED_ENABLED
    led_deinit();
#endif

#if CAMERA_ENABLED
    camera_deinit();
#endif

    system_deinit();
    DEBUG_INFO("System shutdown completed");
}

/**
 * @brief 性能统计
 */
static void performance_stats(void)
{
    uint32_t current_time = system_get_tick();
    
    frame_counter++;
    
    /* 每秒输出一次FPS统计 */
    if (current_time - last_fps_time >= 1000) {
        float fps = (float)frame_counter * 1000.0f / (current_time - last_fps_time);
        DEBUG_INFO("FPS: %.1f, Free heap: %d bytes", fps, system_get_free_heap());
        
        frame_counter = 0;
        last_fps_time = current_time;
    }
}

/**
 * @brief 主循环
 */
static void main_loop(void)
{
    uint32_t last_led_update = 0;
    uint32_t last_ble_update = 0;
    
    DEBUG_INFO("Entering main loop...");
    
    while (system_running) {
        uint32_t current_time = system_get_tick();
        
        /* 处理摄像头数据 */
#if CAMERA_ENABLED
        if (camera_is_frame_ready()) {
            camera_frame_t* frame = camera_get_frame();
            if (frame != NULL) {
                /* 颜色提取和LED更新 */
                sync_engine_process_frame(frame);
                camera_release_frame(frame);
                
                performance_stats();
            }
        }
#endif
        
        /* LED更新 */
#if LED_ENABLED
        if (current_time - last_led_update >= LED_UPDATE_INTERVAL) {
            led_update();
            last_led_update = current_time;
        }
#endif
        
        /* 蓝牙通信处理 */
#if BLE_ENABLED
        if (current_time - last_ble_update >= BLE_UPDATE_INTERVAL) {
            ble_process();
            last_ble_update = current_time;
        }
#endif
        
        /* 系统任务调度 */
        system_task_schedule();
        
        /* 看门狗喂狗 */
#if WATCHDOG_ENABLED
        system_watchdog_feed();
#endif
        
        /* 短暂延时，避免CPU占用过高 */
        system_delay_ms(1);
    }
}

/**
 * @brief 主函数
 * @return 程序退出码
 */
int main(void)
{
    int ret = 0;
    
    /* 系统初始化 */
    ret = system_initialize();
    if (ret != 0) {
        DEBUG_ERROR("System initialization failed, error code: %d", ret);
        return ret;
    }
    
    /* 启动LED测试序列 */
#if LED_ENABLED
    led_test_sequence();
#endif
    
    /* 进入主循环 */
    main_loop();
    
    /* 系统清理 */
    system_deinitialize();
    
    return 0;
}
