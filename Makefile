# LED背景灯同步系统 Makefile
# 基于HPM5321的嵌入式项目构建脚本

# 项目配置
PROJECT_NAME = led_sync_system
VERSION = 1.0.0

# 目录配置
BUILD_DIR = build
SRC_DIR = src
CONFIG_DIR = config
DOCS_DIR = docs
TESTS_DIR = tests

# HPM SDK配置 (需要根据实际路径修改)
HPM_SDK_BASE ?= $(HOME)/hpm_sdk
HPM_TOOLCHAIN = riscv32-unknown-elf

# 编译器配置
CC = $(HPM_TOOLCHAIN)-gcc
CXX = $(HPM_TOOLCHAIN)-g++
AS = $(HPM_TOOLCHAIN)-as
LD = $(HPM_TOOLCHAIN)-ld
OBJCOPY = $(HPM_TOOLCHAIN)-objcopy
OBJDUMP = $(HPM_TOOLCHAIN)-objdump
SIZE = $(HPM_TOOLCHAIN)-size

# 编译选项
CFLAGS = -Wall -Wextra -O2 -g -std=c11
CFLAGS += -ffunction-sections -fdata-sections
CFLAGS += -mcpu=rv32imac -mabi=ilp32
CXXFLAGS = -Wall -Wextra -O2 -g -std=c++17
CXXFLAGS += -ffunction-sections -fdata-sections
CXXFLAGS += -mcpu=rv32imac -mabi=ilp32

# 链接选项
LDFLAGS = -Wl,--gc-sections -Wl,--print-memory-usage
LDFLAGS += -mcpu=rv32imac -mabi=ilp32

# 包含路径
INCLUDES = -I$(SRC_DIR)
INCLUDES += -I$(SRC_DIR)/common
INCLUDES += -I$(SRC_DIR)/drivers
INCLUDES += -I$(SRC_DIR)/algorithm
INCLUDES += -I$(SRC_DIR)/application
INCLUDES += -I$(CONFIG_DIR)

# 如果HPM SDK存在，添加SDK包含路径
ifneq ($(wildcard $(HPM_SDK_BASE)),)
INCLUDES += -I$(HPM_SDK_BASE)/soc/HPM5321/hpm_common
INCLUDES += -I$(HPM_SDK_BASE)/arch/riscv
INCLUDES += -I$(HPM_SDK_BASE)/drivers/inc
endif

# 源文件
SOURCES = $(wildcard $(SRC_DIR)/*.c)
SOURCES += $(wildcard $(SRC_DIR)/*/*.c)
SOURCES += $(wildcard $(SRC_DIR)/*/*/*.c)

# 目标文件
OBJECTS = $(SOURCES:%.c=$(BUILD_DIR)/%.o)

# 默认目标
.PHONY: all
all: $(BUILD_DIR)/$(PROJECT_NAME).elf $(BUILD_DIR)/$(PROJECT_NAME).hex $(BUILD_DIR)/$(PROJECT_NAME).bin

# 创建构建目录
$(BUILD_DIR):
	@mkdir -p $(BUILD_DIR)
	@mkdir -p $(BUILD_DIR)/$(SRC_DIR)
	@mkdir -p $(BUILD_DIR)/$(SRC_DIR)/common
	@mkdir -p $(BUILD_DIR)/$(SRC_DIR)/drivers
	@mkdir -p $(BUILD_DIR)/$(SRC_DIR)/algorithm
	@mkdir -p $(BUILD_DIR)/$(SRC_DIR)/application

# 编译目标文件
$(BUILD_DIR)/%.o: %.c | $(BUILD_DIR)
	@echo "Compiling $<"
	@mkdir -p $(dir $@)
	@$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 链接可执行文件
$(BUILD_DIR)/$(PROJECT_NAME).elf: $(OBJECTS)
	@echo "Linking $@"
	@$(CC) $(LDFLAGS) $^ -o $@
	@$(SIZE) $@

# 生成hex文件
$(BUILD_DIR)/$(PROJECT_NAME).hex: $(BUILD_DIR)/$(PROJECT_NAME).elf
	@echo "Generating $@"
	@$(OBJCOPY) -O ihex $< $@

# 生成bin文件
$(BUILD_DIR)/$(PROJECT_NAME).bin: $(BUILD_DIR)/$(PROJECT_NAME).elf
	@echo "Generating $@"
	@$(OBJCOPY) -O binary $< $@

# 使用CMake构建 (推荐)
.PHONY: cmake
cmake:
	@mkdir -p $(BUILD_DIR)
	@cd $(BUILD_DIR) && cmake ..
	@cd $(BUILD_DIR) && make

# 清理构建文件
.PHONY: clean
clean:
	@echo "Cleaning build files"
	@rm -rf $(BUILD_DIR)

# 完全清理
.PHONY: distclean
distclean: clean
	@rm -f *.log *.tmp

# 代码格式化
.PHONY: format
format:
	@echo "Formatting source code"
	@find $(SRC_DIR) -name "*.c" -o -name "*.h" | xargs clang-format -i
	@find $(CONFIG_DIR) -name "*.h" | xargs clang-format -i

# 静态代码分析
.PHONY: analyze
analyze:
	@echo "Running static analysis"
	@cppcheck --enable=all --inconclusive $(SRC_DIR)

# 生成文档
.PHONY: docs
docs:
	@echo "Generating documentation"
	@doxygen Doxyfile

# 运行单元测试
.PHONY: test
test:
	@echo "Running unit tests"
	@cd $(TESTS_DIR) && make test

# 烧录固件 (需要配置烧录器)
.PHONY: flash
flash: $(BUILD_DIR)/$(PROJECT_NAME).elf
	@echo "Flashing firmware"
	@openocd -f hpm5321.cfg -c "program $< verify reset exit"

# 调试
.PHONY: debug
debug: $(BUILD_DIR)/$(PROJECT_NAME).elf
	@echo "Starting debug session"
	@openocd -f hpm5321.cfg &
	@gdb-multiarch $< -ex "target remote localhost:3333"

# 监控串口输出
.PHONY: monitor
monitor:
	@echo "Monitoring serial output"
	@minicom -D /dev/ttyUSB0 -b 115200

# 显示内存使用
.PHONY: size
size: $(BUILD_DIR)/$(PROJECT_NAME).elf
	@$(SIZE) $<
	@$(OBJDUMP) -h $< | grep -E '\.(text|data|bss)'

# 反汇编
.PHONY: disasm
disasm: $(BUILD_DIR)/$(PROJECT_NAME).elf
	@$(OBJDUMP) -d $< > $(BUILD_DIR)/$(PROJECT_NAME).dis
	@echo "Disassembly saved to $(BUILD_DIR)/$(PROJECT_NAME).dis"

# 显示符号表
.PHONY: symbols
symbols: $(BUILD_DIR)/$(PROJECT_NAME).elf
	@$(OBJDUMP) -t $< | sort > $(BUILD_DIR)/$(PROJECT_NAME).sym
	@echo "Symbol table saved to $(BUILD_DIR)/$(PROJECT_NAME).sym"

# 检查依赖
.PHONY: check-deps
check-deps:
	@echo "Checking dependencies..."
	@which $(CC) > /dev/null || (echo "Error: $(CC) not found" && exit 1)
	@which $(OBJCOPY) > /dev/null || (echo "Error: $(OBJCOPY) not found" && exit 1)
	@which cmake > /dev/null || (echo "Warning: cmake not found")
	@echo "Dependencies check completed"

# 显示帮助信息
.PHONY: help
help:
	@echo "LED背景灯同步系统构建脚本"
	@echo ""
	@echo "可用目标:"
	@echo "  all        - 构建所有目标文件 (默认)"
	@echo "  cmake      - 使用CMake构建 (推荐)"
	@echo "  clean      - 清理构建文件"
	@echo "  distclean  - 完全清理"
	@echo "  format     - 格式化源代码"
	@echo "  analyze    - 静态代码分析"
	@echo "  docs       - 生成文档"
	@echo "  test       - 运行单元测试"
	@echo "  flash      - 烧录固件"
	@echo "  debug      - 启动调试会话"
	@echo "  monitor    - 监控串口输出"
	@echo "  size       - 显示内存使用"
	@echo "  disasm     - 生成反汇编文件"
	@echo "  symbols    - 生成符号表"
	@echo "  check-deps - 检查依赖"
	@echo "  help       - 显示此帮助信息"
	@echo ""
	@echo "环境变量:"
	@echo "  HPM_SDK_BASE - HPM SDK路径 (默认: $(HPM_SDK_BASE))"
	@echo ""
	@echo "示例:"
	@echo "  make cmake              # 使用CMake构建"
	@echo "  make flash              # 烧录固件"
	@echo "  make HPM_SDK_BASE=/opt/hpm_sdk cmake  # 指定SDK路径"

# 显示项目信息
.PHONY: info
info:
	@echo "项目: $(PROJECT_NAME)"
	@echo "版本: $(VERSION)"
	@echo "构建目录: $(BUILD_DIR)"
	@echo "源码目录: $(SRC_DIR)"
	@echo "HPM SDK: $(HPM_SDK_BASE)"
	@echo "工具链: $(HPM_TOOLCHAIN)"
	@echo "编译器: $(CC)"
