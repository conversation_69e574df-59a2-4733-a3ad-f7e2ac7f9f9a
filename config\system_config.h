/**
 * @file system_config.h
 * @brief 系统配置文件
 * <AUTHOR> Sync System Team
 * @date 2025-07-25
 */

#ifndef SYSTEM_CONFIG_H
#define SYSTEM_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

/* ========== 系统基础配置 ========== */
#define SYSTEM_VERSION_MAJOR    1
#define SYSTEM_VERSION_MINOR    0
#define SYSTEM_VERSION_PATCH    0

#define SYSTEM_CLOCK_FREQ       200000000UL  // 200MHz
#define SYSTICK_FREQ            1000UL       // 1ms

/* ========== 摄像头配置 ========== */
#define CAMERA_ENABLED          1
#define CAMERA_WIDTH            1280
#define CAMERA_HEIGHT           720
#define CAMERA_FPS              30
#define CAMERA_PIXEL_FORMAT     CAMERA_FORMAT_RGB565
#define CAMERA_BUFFER_COUNT     2

/* 摄像头接口配置 */
#define CAMERA_INTERFACE        CAMERA_IF_USB
#define CAMERA_USB_VID          0x1234
#define CAMERA_USB_PID          0x5678

/* ========== LED配置 ========== */
#define LED_ENABLED             1
#define LED_TYPE                LED_TYPE_WS2812B
#define LED_COUNT_MAX           300
#define LED_COUNT_DEFAULT       60
#define LED_SPI_INSTANCE        1
#define LED_SPI_BAUDRATE        3200000UL    // 3.2MHz for WS2812B

/* LED颜色配置 */
#define LED_COLOR_DEPTH         24           // 24位RGB
#define LED_BRIGHTNESS_MAX      255
#define LED_BRIGHTNESS_DEFAULT  128

/* ========== 蓝牙配置 ========== */
#define BLE_ENABLED             1
#define BLE_DEVICE_NAME         "LED_Sync_System"
#define BLE_UART_INSTANCE       2
#define BLE_UART_BAUDRATE       115200UL

/* BLE服务UUID (自定义) */
#define BLE_SERVICE_UUID        "12345678-1234-1234-1234-123456789ABC"
#define BLE_CHAR_CONFIG_UUID    "12345678-1234-1234-1234-123456789ABD"
#define BLE_CHAR_STATUS_UUID    "12345678-1234-1234-1234-123456789ABE"

/* ========== 算法配置 ========== */
#define ALGORITHM_ENABLED       1

/* 颜色提取算法配置 */
#define COLOR_EXTRACT_METHOD    COLOR_METHOD_KMEANS
#define COLOR_CLUSTER_COUNT     5
#define COLOR_SAMPLE_RATE       4            // 每4个像素采样1个

/* 边缘检测配置 */
#define EDGE_DETECT_ENABLED     1
#define EDGE_THRESHOLD          50

/* 颜色平滑配置 */
#define COLOR_SMOOTH_ENABLED    1
#define COLOR_SMOOTH_FACTOR     0.8f
#define COLOR_TRANSITION_TIME   100          // ms

/* ========== 内存配置 ========== */
#define HEAP_SIZE               (64 * 1024)  // 64KB
#define STACK_SIZE              (8 * 1024)   // 8KB

/* 图像缓冲区配置 */
#define IMAGE_BUFFER_SIZE       (CAMERA_WIDTH * CAMERA_HEIGHT * 2)  // RGB565
#define LED_BUFFER_SIZE         (LED_COUNT_MAX * 3)                 // RGB

/* ========== 性能配置 ========== */
#define FRAME_PROCESS_TIMEOUT   50           // ms
#define LED_UPDATE_INTERVAL     20           // ms
#define BLE_UPDATE_INTERVAL     100          // ms

/* ========== 调试配置 ========== */
#define DEBUG_ENABLED           1
#define DEBUG_UART_INSTANCE     0
#define DEBUG_UART_BAUDRATE     115200UL

#if DEBUG_ENABLED
#define DEBUG_LEVEL_ERROR       1
#define DEBUG_LEVEL_WARN        1
#define DEBUG_LEVEL_INFO        1
#define DEBUG_LEVEL_DEBUG       0
#endif

/* ========== GPIO配置 ========== */
/* 状态LED */
#define STATUS_LED_PORT         GPIO_PORT_A
#define STATUS_LED_PIN          GPIO_PIN_0

/* 用户按键 */
#define USER_KEY_PORT           GPIO_PORT_A
#define USER_KEY_PIN            GPIO_PIN_1

/* LED数据输出 */
#define LED_DATA_PORT           GPIO_PORT_B
#define LED_DATA_PIN            GPIO_PIN_0

/* ========== 电源管理配置 ========== */
#define POWER_MGMT_ENABLED      1
#define POWER_MONITOR_ADC       ADC_INSTANCE_0
#define POWER_MONITOR_CHANNEL   0
#define POWER_VOLTAGE_MIN       3000         // mV
#define POWER_VOLTAGE_MAX       5500         // mV

/* ========== 看门狗配置 ========== */
#define WATCHDOG_ENABLED        1
#define WATCHDOG_TIMEOUT        5000         // ms

/* ========== 枚举定义 ========== */
typedef enum {
    CAMERA_FORMAT_RGB565 = 0,
    CAMERA_FORMAT_YUV422,
    CAMERA_FORMAT_RGB888
} camera_format_t;

typedef enum {
    CAMERA_IF_USB = 0,
    CAMERA_IF_MIPI
} camera_interface_t;

typedef enum {
    LED_TYPE_WS2812B = 0,
    LED_TYPE_WS2811,
    LED_TYPE_SK6812
} led_type_t;

typedef enum {
    COLOR_METHOD_AVERAGE = 0,
    COLOR_METHOD_DOMINANT,
    COLOR_METHOD_KMEANS
} color_extract_method_t;

#ifdef __cplusplus
}
#endif

#endif /* SYSTEM_CONFIG_H */
