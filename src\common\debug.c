/**
 * @file debug.c
 * @brief 调试输出实现
 * <AUTHOR> Sync System Team
 * @date 2025-07-25
 */

#include "debug.h"
#include "system_config.h"
#include "system_init.h"
#include <stdarg.h>
#include <stdio.h>
#include <string.h>

/* ========== 私有变量 ========== */
#if DEBUG_ENABLED
static uint8_t g_debug_level = DEBUG_LEVEL_INFO;
static bool g_debug_enabled = true;
static char g_debug_buffer[256];
#endif

/* ========== 私有函数声明 ========== */
#if DEBUG_ENABLED
static int debug_uart_init(void);
static void debug_uart_send_char(char c);
static void debug_uart_send_string(const char* str);
#endif

/* ========== 公共接口实现 ========== */

/**
 * @brief 调试模块初始化
 */
int debug_init(void)
{
#if DEBUG_ENABLED
    int ret = debug_uart_init();
    if (ret != 0) {
        return ret;
    }
    
    g_debug_enabled = true;
    g_debug_level = DEBUG_LEVEL_INFO;
    
    /* 输出启动信息 */
    debug_printf("\r\n");
    debug_printf("=====================================\r\n");
    debug_printf("LED Sync System v%d.%d.%d\r\n", 
                 SYSTEM_VERSION_MAJOR, SYSTEM_VERSION_MINOR, SYSTEM_VERSION_PATCH);
    debug_printf("Build: %s %s\r\n", __DATE__, __TIME__);
    debug_printf("Debug Level: %d\r\n", g_debug_level);
    debug_printf("=====================================\r\n");
    
    return 0;
#else
    return 0;
#endif
}

/**
 * @brief 调试模块去初始化
 */
void debug_deinit(void)
{
#if DEBUG_ENABLED
    if (g_debug_enabled) {
        debug_printf("Debug module deinitialized\r\n");
        g_debug_enabled = false;
    }
#endif
}

/**
 * @brief 设置调试级别
 */
void debug_set_level(uint8_t level)
{
#if DEBUG_ENABLED
    g_debug_level = level;
    debug_printf("Debug level set to: %d\r\n", level);
#else
    (void)level;
#endif
}

/**
 * @brief 获取调试级别
 */
uint8_t debug_get_level(void)
{
#if DEBUG_ENABLED
    return g_debug_level;
#else
    return 0;
#endif
}

/**
 * @brief 调试输出使能/禁用
 */
void debug_enable(bool enable)
{
#if DEBUG_ENABLED
    g_debug_enabled = enable;
    if (enable) {
        debug_printf("Debug output enabled\r\n");
    }
#else
    (void)enable;
#endif
}

/**
 * @brief 检查调试输出是否使能
 */
bool debug_is_enabled(void)
{
#if DEBUG_ENABLED
    return g_debug_enabled;
#else
    return false;
#endif
}

#if DEBUG_ENABLED

/**
 * @brief 调试输出函数
 */
void debug_printf(const char* format, ...)
{
    if (!g_debug_enabled) {
        return;
    }
    
    va_list args;
    va_start(args, format);
    
    int len = vsnprintf(g_debug_buffer, sizeof(g_debug_buffer), format, args);
    
    va_end(args);
    
    if (len > 0) {
        /* 确保字符串以null结尾 */
        if (len >= (int)sizeof(g_debug_buffer)) {
            len = sizeof(g_debug_buffer) - 1;
        }
        g_debug_buffer[len] = '\0';
        
        debug_uart_send_string(g_debug_buffer);
    }
}

/**
 * @brief 十六进制数据输出
 */
void debug_print_hex(const uint8_t* data, uint32_t length)
{
    if (!g_debug_enabled || data == NULL) {
        return;
    }
    
    for (uint32_t i = 0; i < length; i++) {
        debug_printf("%02X ", data[i]);
        
        /* 每16个字节换行 */
        if ((i + 1) % 16 == 0) {
            debug_printf("\r\n");
        }
    }
    
    /* 如果最后一行不满16个字节，补充换行 */
    if (length % 16 != 0) {
        debug_printf("\r\n");
    }
}

/**
 * @brief 打印时间戳
 */
void debug_print_timestamp(void)
{
    if (!g_debug_enabled) {
        return;
    }
    
    uint32_t tick = system_get_tick();
    uint32_t seconds = tick / 1000;
    uint32_t milliseconds = tick % 1000;
    
    debug_printf("[%06u.%03u] ", seconds, milliseconds);
}

/* ========== 私有函数实现 ========== */

/**
 * @brief 调试UART初始化
 */
static int debug_uart_init(void)
{
    /* TODO: 实现具体的UART初始化 */
    /* 配置UART引脚 */
    /* 设置波特率 */
    /* 配置数据位、停止位、校验位 */
    /* 使能UART */
    
    return 0;
}

/**
 * @brief 发送单个字符
 */
static void debug_uart_send_char(char c)
{
    /* TODO: 实现具体的字符发送 */
    /* 等待发送缓冲区空 */
    /* 写入字符到发送寄存器 */
    
    /* 临时实现：使用标准输出 */
    putchar(c);
}

/**
 * @brief 发送字符串
 */
static void debug_uart_send_string(const char* str)
{
    if (str == NULL) {
        return;
    }
    
    while (*str) {
        debug_uart_send_char(*str);
        str++;
    }
}

#endif /* DEBUG_ENABLED */
