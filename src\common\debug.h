/**
 * @file debug.h
 * @brief 调试输出接口定义
 * <AUTHOR> Sync System Team
 * @date 2025-07-25
 */

#ifndef DEBUG_H
#define DEBUG_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdio.h>
#include "system_config.h"

/* ========== 调试级别定义 ========== */
#define DEBUG_LEVEL_NONE    0
#define DEBUG_LEVEL_ERROR   1
#define DEBUG_LEVEL_WARN    2
#define DEBUG_LEVEL_INFO    3
#define DEBUG_LEVEL_DEBUG   4

/* ========== 调试输出宏 ========== */
#if DEBUG_ENABLED

/* 调试输出函数声明 */
void debug_printf(const char* format, ...);
void debug_print_hex(const uint8_t* data, uint32_t length);
void debug_print_timestamp(void);

/* 错误级别输出 */
#if DEBUG_LEVEL_ERROR
#define DEBUG_ERROR(fmt, ...) do { \
    debug_print_timestamp(); \
    debug_printf("[ERROR] " fmt "\r\n", ##__VA_ARGS__); \
} while(0)
#else
#define DEBUG_ERROR(fmt, ...)
#endif

/* 警告级别输出 */
#if DEBUG_LEVEL_WARN
#define DEBUG_WARN(fmt, ...) do { \
    debug_print_timestamp(); \
    debug_printf("[WARN ] " fmt "\r\n", ##__VA_ARGS__); \
} while(0)
#else
#define DEBUG_WARN(fmt, ...)
#endif

/* 信息级别输出 */
#if DEBUG_LEVEL_INFO
#define DEBUG_INFO(fmt, ...) do { \
    debug_print_timestamp(); \
    debug_printf("[INFO ] " fmt "\r\n", ##__VA_ARGS__); \
} while(0)
#else
#define DEBUG_INFO(fmt, ...)
#endif

/* 调试级别输出 */
#if DEBUG_LEVEL_DEBUG
#define DEBUG_DEBUG(fmt, ...) do { \
    debug_print_timestamp(); \
    debug_printf("[DEBUG] " fmt "\r\n", ##__VA_ARGS__); \
} while(0)
#else
#define DEBUG_DEBUG(fmt, ...)
#endif

/* 十六进制数据输出 */
#define DEBUG_HEX(data, len) do { \
    debug_print_timestamp(); \
    debug_printf("[HEX  ] "); \
    debug_print_hex((const uint8_t*)(data), (len)); \
    debug_printf("\r\n"); \
} while(0)

/* 函数进入/退出跟踪 */
#if DEBUG_LEVEL_DEBUG
#define DEBUG_ENTER() DEBUG_DEBUG("Enter %s", __FUNCTION__)
#define DEBUG_EXIT() DEBUG_DEBUG("Exit %s", __FUNCTION__)
#define DEBUG_EXIT_RET(ret) DEBUG_DEBUG("Exit %s, ret=%d", __FUNCTION__, (ret))
#else
#define DEBUG_ENTER()
#define DEBUG_EXIT()
#define DEBUG_EXIT_RET(ret)
#endif

/* 断言宏 */
#define DEBUG_ASSERT(expr) do { \
    if (!(expr)) { \
        DEBUG_ERROR("Assertion failed: %s, file %s, line %d", \
                   #expr, __FILE__, __LINE__); \
        while(1); \
    } \
} while(0)

#else /* DEBUG_ENABLED */

/* 调试功能禁用时的空宏 */
#define DEBUG_ERROR(fmt, ...)
#define DEBUG_WARN(fmt, ...)
#define DEBUG_INFO(fmt, ...)
#define DEBUG_DEBUG(fmt, ...)
#define DEBUG_HEX(data, len)
#define DEBUG_ENTER()
#define DEBUG_EXIT()
#define DEBUG_EXIT_RET(ret)
#define DEBUG_ASSERT(expr)

#endif /* DEBUG_ENABLED */

/* ========== 调试接口函数 ========== */

/**
 * @brief 调试模块初始化
 * @return 0: 成功, 负数: 错误码
 */
int debug_init(void);

/**
 * @brief 调试模块去初始化
 */
void debug_deinit(void);

/**
 * @brief 设置调试级别
 * @param level 调试级别
 */
void debug_set_level(uint8_t level);

/**
 * @brief 获取调试级别
 * @return 当前调试级别
 */
uint8_t debug_get_level(void);

/**
 * @brief 调试输出使能/禁用
 * @param enable true: 使能, false: 禁用
 */
void debug_enable(bool enable);

/**
 * @brief 检查调试输出是否使能
 * @return true: 使能, false: 禁用
 */
bool debug_is_enabled(void);

/* ========== 性能分析宏 ========== */
#if DEBUG_ENABLED && DEBUG_LEVEL_DEBUG

/* 性能计时器 */
typedef struct {
    uint32_t start_time;
    uint32_t end_time;
    const char* name;
} debug_timer_t;

#define DEBUG_TIMER_START(timer, timer_name) do { \
    (timer).name = (timer_name); \
    (timer).start_time = system_get_tick(); \
} while(0)

#define DEBUG_TIMER_END(timer) do { \
    (timer).end_time = system_get_tick(); \
    DEBUG_DEBUG("Timer [%s]: %u ms", (timer).name, \
               (timer).end_time - (timer).start_time); \
} while(0)

/* 内存使用跟踪 */
#define DEBUG_MEMORY_USAGE() do { \
    DEBUG_DEBUG("Free heap: %u bytes", system_get_free_heap()); \
} while(0)

#else

#define DEBUG_TIMER_START(timer, timer_name)
#define DEBUG_TIMER_END(timer)
#define DEBUG_MEMORY_USAGE()

#endif

#ifdef __cplusplus
}
#endif

#endif /* DEBUG_H */
