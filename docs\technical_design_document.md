# LED背景灯同步系统 - 技术设计文档

## 文档信息

- **项目名称**: LED背景灯同步系统
- **版本**: v1.0.0
- **日期**: 2025-07-25
- **作者**: LED Sync System Team

## 1. 项目概述

### 1.1 项目背景
本项目旨在开发一个基于HPM5321主控的智能LED背景灯系统，通过摄像头实时采集电视屏幕颜色，并同步控制LED灯带显示相应的氛围灯效果，提升观影体验。

### 1.2 核心功能
- **实时颜色采集**: 720p摄像头30fps实时图像采集
- **智能颜色分析**: 多种颜色提取算法，支持主色调、平均色、K-means聚类
- **LED同步控制**: 支持WS2812B等可编程LED，多种映射模式
- **无线控制**: 蓝牙BLE 5.0通信，手机APP远程配置
- **低延迟响应**: 端到端延迟小于100ms

### 1.3 技术指标
| 指标 | 规格 |
|------|------|
| 主控芯片 | HPM5321 (RISC-V 32位) |
| 摄像头分辨率 | 720p (1280x720) |
| 帧率 | 30fps |
| LED类型 | WS2812B RGB可编程LED |
| LED数量 | 最大300颗 |
| 通信方式 | 蓝牙BLE 5.0 |
| 响应延迟 | < 100ms |
| 功耗 | < 5W |

## 2. 系统架构设计

### 2.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application)                      │
├─────────────────────────────────────────────────────────────┤
│  同步引擎  │  配置管理  │  BLE服务  │  状态监控  │  效果控制  │
├─────────────────────────────────────────────────────────────┤
│                    算法层 (Algorithm)                        │
├─────────────────────────────────────────────────────────────┤
│ 颜色提取 │ 边缘检测 │ 平滑滤波 │ K-means │ 颜色空间转换    │
├─────────────────────────────────────────────────────────────┤
│                    驱动层 (Drivers)                          │
├─────────────────────────────────────────────────────────────┤
│ 摄像头驱动 │ LED驱动 │ 蓝牙驱动 │ 定时器驱动 │ GPIO驱动    │
├─────────────────────────────────────────────────────────────┤
│                    HAL层 (Hardware)                         │
├─────────────────────────────────────────────────────────────┤
│    USB    │   SPI    │   UART   │   I2C    │    PWM    │
├─────────────────────────────────────────────────────────────┤
│                   HPM5321 硬件平台                           │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 模块划分

#### 2.2.1 摄像头采集模块
- **功能**: 720p图像实时采集和预处理
- **接口**: USB 2.0 Host
- **输出格式**: RGB565/YUV422/RGB888
- **缓冲机制**: 双缓冲区，避免数据丢失

#### 2.2.2 颜色提取算法模块
- **平均颜色算法**: 计算图像整体平均颜色
- **主色调算法**: 基于直方图的主要颜色提取
- **K-means聚类**: 提取多个主要颜色簇
- **边缘检测**: Sobel算子边缘增强
- **颜色平滑**: 指数移动平均滤波

#### 2.2.3 LED驱动模块
- **协议支持**: WS2812B/WS2811/SK6812
- **控制方式**: SPI + DMA高效传输
- **颜色深度**: 24位RGB (8位/通道)
- **效果支持**: 静态、呼吸、彩虹、追逐、渐变

#### 2.2.4 蓝牙通信模块
- **协议版本**: BLE 5.0
- **服务定义**: 自定义GATT服务
- **功能**: 参数配置、状态监控、固件升级
- **安全**: 数据包校验和验证

#### 2.2.5 同步引擎模块
- **核心功能**: 协调各模块工作
- **映射模式**: 线性、环形、矩阵、自定义
- **性能优化**: 多级缓存、异步处理
- **配置管理**: 参数持久化存储

## 3. 接口设计

### 3.1 硬件接口

#### 3.1.1 摄像头接口
```c
// 摄像头配置结构
typedef struct {
    uint16_t width;                 // 图像宽度
    uint16_t height;                // 图像高度
    uint8_t fps;                    // 帧率
    camera_format_t format;         // 像素格式
    camera_interface_t interface;   // 接口类型
    uint8_t brightness;             // 亮度 (0-255)
    uint8_t contrast;               // 对比度 (0-255)
    uint8_t saturation;             // 饱和度 (0-255)
    bool auto_exposure;             // 自动曝光
    bool auto_white_balance;        // 自动白平衡
} camera_config_t;

// 主要接口函数
int camera_init(void);
int camera_start_streaming(void);
camera_frame_t* camera_get_frame(void);
void camera_release_frame(camera_frame_t* frame);
```

#### 3.1.2 LED控制接口
```c
// LED配置结构
typedef struct {
    uint16_t led_count;         // LED数量
    led_type_t led_type;        // LED类型
    uint8_t brightness;         // 全局亮度 (0-255)
    bool gamma_correction;      // 伽马校正使能
    uint8_t color_order;        // 颜色顺序
    uint32_t update_rate;       // 更新频率 (Hz)
} led_config_t;

// 主要接口函数
int led_init(void);
int led_set_pixel(uint16_t index, led_rgb_t color);
int led_set_all(led_rgb_t color);
int led_update(void);
int led_start_effect(const led_effect_params_t* params);
```

#### 3.1.3 蓝牙通信接口
```c
// BLE数据包结构
typedef struct {
    uint8_t command;            // 命令类型
    uint8_t length;             // 数据长度
    uint8_t data[32];           // 数据内容
    uint8_t checksum;           // 校验和
} ble_packet_t;

// 主要接口函数
int ble_init(void);
int ble_start_advertising(void);
int ble_send_packet(const ble_packet_t* packet);
void ble_set_event_callback(ble_event_callback_t callback);
```

### 3.2 软件接口

#### 3.2.1 颜色提取接口
```c
// 颜色提取结果
typedef struct {
    led_rgb_t dominant_color;       // 主色调
    led_rgb_t average_color;        // 平均颜色
    color_stat_t top_colors[5];     // 前5种颜色
    uint8_t color_count;            // 有效颜色数量
    float brightness;               // 整体亮度
    float contrast;                 // 对比度
    float saturation;               // 饱和度
} color_extract_result_t;

// 主要接口函数
int color_extract_init(void);
int color_extract_from_frame(const camera_frame_t* frame, 
                             color_extract_result_t* result);
led_rgb_t color_algorithm_average(const uint8_t* data, ...);
led_rgb_t color_algorithm_dominant(const uint8_t* data, ...);
int color_algorithm_kmeans(const uint8_t* data, ...);
```

#### 3.2.2 同步引擎接口
```c
// 同步引擎配置
typedef struct {
    sync_mode_t sync_mode;              // 同步模式
    led_mapping_t led_mapping;          // LED映射模式
    uint8_t led_count;                  // LED数量
    uint8_t brightness;                 // 亮度
    uint8_t saturation_boost;           // 饱和度增强
    uint8_t color_temperature;          // 色温调节
    bool auto_brightness;               // 自动亮度
    bool smooth_transition;             // 平滑过渡
    uint16_t transition_time;           // 过渡时间 (ms)
    uint8_t update_rate;                // 更新频率 (Hz)
    color_extract_config_t color_config; // 颜色提取配置
} sync_engine_config_t;

// 主要接口函数
int sync_engine_init(void);
int sync_engine_start(void);
int sync_engine_process_frame(const camera_frame_t* frame);
int sync_engine_set_config(const sync_engine_config_t* config);
int sync_engine_handle_ble_command(const ble_packet_t* packet);
```

## 4. 算法设计

### 4.1 颜色提取算法

#### 4.1.1 平均颜色算法
```c
led_rgb_t color_algorithm_average(const uint8_t* data, uint16_t width, 
                                  uint16_t height, camera_format_t format, 
                                  uint8_t sample_rate)
{
    uint32_t r_sum = 0, g_sum = 0, b_sum = 0;
    uint32_t pixel_count = 0;
    
    // 按采样率遍历像素
    for (uint16_t y = 0; y < height; y += sample_rate) {
        for (uint16_t x = 0; x < width; x += sample_rate) {
            led_rgb_t pixel = extract_pixel(data, x, y, format);
            r_sum += pixel.red;
            g_sum += pixel.green;
            b_sum += pixel.blue;
            pixel_count++;
        }
    }
    
    // 计算平均值
    led_rgb_t result = {
        .red = pixel_count > 0 ? r_sum / pixel_count : 0,
        .green = pixel_count > 0 ? g_sum / pixel_count : 0,
        .blue = pixel_count > 0 ? b_sum / pixel_count : 0
    };
    
    return result;
}
```

#### 4.1.2 K-means聚类算法
```c
int color_algorithm_kmeans(const uint8_t* data, uint16_t width, uint16_t height,
                           camera_format_t format, uint8_t cluster_count, 
                           uint8_t sample_rate, led_rgb_t* colors)
{
    // 1. 采样像素点
    led_rgb_t* samples = sample_pixels(data, width, height, format, sample_rate);
    
    // 2. 初始化聚类中心
    kmeans_center_t* centers = init_centers(samples, sample_count, cluster_count);
    
    // 3. 迭代优化
    for (int iter = 0; iter < MAX_ITERATIONS; iter++) {
        // 分配样本到最近的聚类中心
        assign_samples_to_centers(samples, sample_count, centers, cluster_count);
        
        // 更新聚类中心
        update_centers(samples, sample_count, centers, cluster_count);
        
        // 检查收敛
        if (check_convergence(centers, cluster_count)) {
            break;
        }
    }
    
    // 4. 输出结果
    return extract_final_colors(centers, cluster_count, colors);
}
```

### 4.2 颜色平滑算法
```c
led_rgb_t color_smooth_filter(led_rgb_t current_color)
{
    static led_rgb_t last_color = {0};
    static bool initialized = false;
    
    if (!initialized) {
        last_color = current_color;
        initialized = true;
        return current_color;
    }
    
    // 指数移动平均滤波
    float alpha = 0.8f; // 平滑因子
    
    led_rgb_t result;
    result.red = (uint8_t)(alpha * current_color.red + (1.0f - alpha) * last_color.red);
    result.green = (uint8_t)(alpha * current_color.green + (1.0f - alpha) * last_color.green);
    result.blue = (uint8_t)(alpha * current_color.blue + (1.0f - alpha) * last_color.blue);
    
    last_color = result;
    return result;
}
```

### 4.3 LED映射算法

#### 4.3.1 线性映射
所有LED显示相同的主色调，适用于简单的氛围灯效果。

#### 4.3.2 环形映射
基于主色调创建彩虹效果，LED颜色在主色调周围变化。

#### 4.3.3 区域映射
将屏幕分为多个区域，每个区域对应一组LED，实现更精细的颜色同步。

## 5. 性能优化

### 5.1 处理流水线
```
摄像头采集 → 颜色提取 → 颜色映射 → LED更新
     ↓           ↓          ↓         ↓
   30fps      < 50ms     < 20ms    < 30ms
```

### 5.2 内存优化
- **双缓冲机制**: 避免数据竞争
- **内存池管理**: 减少动态分配开销
- **数据压缩**: 降低内存使用

### 5.3 CPU优化
- **SIMD指令**: 加速颜色计算
- **查找表**: 预计算常用函数
- **异步处理**: 并行执行多个任务

### 5.4 功耗优化
- **动态时钟**: 根据负载调整频率
- **睡眠模式**: 空闲时进入低功耗
- **LED亮度**: 自适应亮度控制

## 6. 通信协议设计

### 6.1 BLE服务定义

#### 6.1.1 服务UUID
- **主服务**: `12345678-1234-1234-1234-123456789ABC`
- **配置特征**: `12345678-1234-1234-1234-123456789ABD`
- **状态特征**: `12345678-1234-1234-1234-123456789ABE`

#### 6.1.2 命令格式
```c
typedef enum {
    BLE_CMD_GET_STATUS = 0x01,      // 获取状态
    BLE_CMD_SET_CONFIG = 0x02,      // 设置配置
    BLE_CMD_GET_CONFIG = 0x03,      // 获取配置
    BLE_CMD_SET_COLOR = 0x04,       // 设置颜色
    BLE_CMD_SET_BRIGHTNESS = 0x05,  // 设置亮度
    BLE_CMD_START_SYNC = 0x06,      // 开始同步
    BLE_CMD_STOP_SYNC = 0x07,       // 停止同步
    BLE_CMD_SET_EFFECT = 0x08,      // 设置效果
    BLE_CMD_FIRMWARE_UPDATE = 0x09, // 固件升级
    BLE_CMD_RESET = 0x0A            // 系统重置
} ble_command_t;
```

#### 6.1.3 数据包结构
```c
typedef struct {
    uint8_t command;            // 命令类型
    uint8_t length;             // 数据长度
    uint8_t data[32];           // 数据内容
    uint8_t checksum;           // 校验和
} ble_packet_t;
```

### 6.2 状态数据格式
```c
typedef struct {
    uint8_t system_state;       // 系统状态
    uint8_t sync_enabled;       // 同步使能
    uint8_t led_count;          // LED数量
    uint8_t brightness;         // 亮度
    uint16_t fps;               // 当前FPS
    uint32_t uptime;            // 运行时间
    uint16_t free_memory;       // 空闲内存
} ble_status_data_t;
```

## 7. 错误处理与调试

### 7.1 错误码定义
```c
// 系统错误码
#define SYSTEM_OK                0
#define SYSTEM_ERROR            -1
#define SYSTEM_ERROR_PARAM      -2
#define SYSTEM_ERROR_MEMORY     -3
#define SYSTEM_ERROR_TIMEOUT    -4

// 摄像头错误码
#define CAMERA_OK                   0
#define CAMERA_ERROR_NO_DEVICE     -8
#define CAMERA_ERROR_IO            -9

// LED错误码
#define LED_OK                  0
#define LED_ERROR_INDEX        -8

// BLE错误码
#define BLE_OK                  0
#define BLE_ERROR_NOT_CONNECTED -8
#define BLE_ERROR_CHECKSUM     -9
```

### 7.2 调试接口
```c
// 调试级别
#define DEBUG_LEVEL_ERROR   1
#define DEBUG_LEVEL_WARN    2
#define DEBUG_LEVEL_INFO    3
#define DEBUG_LEVEL_DEBUG   4

// 调试宏
#define DEBUG_ERROR(fmt, ...) debug_printf("[ERROR] " fmt "\r\n", ##__VA_ARGS__)
#define DEBUG_WARN(fmt, ...)  debug_printf("[WARN ] " fmt "\r\n", ##__VA_ARGS__)
#define DEBUG_INFO(fmt, ...)  debug_printf("[INFO ] " fmt "\r\n", ##__VA_ARGS__)
#define DEBUG_DEBUG(fmt, ...) debug_printf("[DEBUG] " fmt "\r\n", ##__VA_ARGS__)
```

### 7.3 性能监控
```c
// 性能计时器
typedef struct {
    uint32_t start_time;
    uint32_t end_time;
    const char* name;
} debug_timer_t;

#define DEBUG_TIMER_START(timer, name) \
    (timer).name = (name); \
    (timer).start_time = system_get_tick()

#define DEBUG_TIMER_END(timer) \
    (timer).end_time = system_get_tick(); \
    DEBUG_DEBUG("Timer [%s]: %u ms", (timer).name, \
               (timer).end_time - (timer).start_time)
```

## 8. 测试与验证

### 8.1 单元测试
- **摄像头模块**: 图像采集、格式转换、帧率测试
- **颜色算法**: 算法精度、性能基准测试
- **LED驱动**: 颜色显示、时序验证、效果测试
- **蓝牙通信**: 连接稳定性、数据传输、协议测试

### 8.2 集成测试
- **端到端延迟**: 从图像采集到LED显示的总延迟
- **颜色准确性**: 显示颜色与屏幕颜色的匹配度
- **系统稳定性**: 长时间运行稳定性测试
- **功耗测试**: 不同工作模式下的功耗测量

### 8.3 性能基准
```c
// 性能测试结果
typedef struct {
    uint32_t camera_fps;            // 摄像头帧率
    uint32_t color_extract_time;    // 颜色提取时间 (us)
    uint32_t led_update_time;       // LED更新时间 (us)
    uint32_t total_latency;         // 总延迟 (ms)
    uint32_t cpu_usage;             // CPU使用率 (%)
    uint32_t memory_usage;          // 内存使用率 (%)
    uint32_t power_consumption;     // 功耗 (mW)
} performance_metrics_t;
```

## 9. 部署与维护

### 9.1 固件升级
- **OTA升级**: 通过蓝牙进行无线固件升级
- **版本管理**: 固件版本号和兼容性检查
- **回滚机制**: 升级失败时自动回滚到上一版本

### 9.2 配置管理
- **参数持久化**: 配置参数保存到Flash
- **出厂重置**: 恢复默认配置功能
- **配置导入导出**: 通过蓝牙导入导出配置文件

### 9.3 故障诊断
- **自检功能**: 系统启动时自动检测各模块状态
- **错误日志**: 记录系统运行中的错误信息
- **远程诊断**: 通过蓝牙获取系统状态和日志

## 10. 使用指南

### 10.1 硬件连接
1. **摄像头连接**: 将USB摄像头连接到HPM5321的USB Host接口
2. **LED连接**: 将WS2812B LED灯带的数据线连接到指定的SPI引脚
3. **电源连接**: 确保系统和LED灯带有足够的电源供应
4. **蓝牙模块**: 连接蓝牙模块到UART接口

### 10.2 软件配置
1. **编译固件**: 使用提供的CMakeLists.txt编译项目
2. **烧录固件**: 通过OpenOCD或其他工具烧录到HPM5321
3. **首次配置**: 通过蓝牙连接进行初始配置
4. **参数调优**: 根据实际环境调整颜色提取和LED参数

### 10.3 手机APP使用
1. **设备发现**: 搜索并连接LED同步设备
2. **实时控制**: 调整亮度、颜色、效果等参数
3. **模式切换**: 在不同同步模式间切换
4. **状态监控**: 查看系统运行状态和性能指标

## 11. 附录

### 11.1 引脚定义
| 功能 | 引脚 | 说明 |
|------|------|------|
| LED数据 | PB0 | WS2812B数据输出 |
| 状态LED | PA0 | 系统状态指示 |
| 用户按键 | PA1 | 用户输入 |
| 蓝牙UART | UART2 | 蓝牙模块通信 |
| 调试UART | UART0 | 调试输出 |

### 11.2 内存分配
| 模块 | 大小 | 说明 |
|------|------|------|
| 图像缓冲区 | 3.6MB | 双缓冲720p RGB |
| LED缓冲区 | 900B | 300个LED颜色数据 |
| 算法缓冲区 | 64KB | 颜色提取工作区 |
| 系统堆栈 | 8KB | 系统调用栈 |
| 总计 | ~4MB | 包含代码和数据 |

### 11.3 性能指标
| 指标 | 目标值 | 实测值 |
|------|--------|--------|
| 端到端延迟 | < 100ms | ~80ms |
| 摄像头帧率 | 30fps | 30fps |
| CPU使用率 | < 80% | ~65% |
| 内存使用 | < 256KB | ~200KB |
| 功耗 | < 5W | ~3.8W |

---

**文档版本**: v1.0.0
**最后更新**: 2025-07-25
**维护者**: LED Sync System Team
