# LED背景灯同步系统 - 基于HPM5321
cmake_minimum_required(VERSION 3.16)

# 项目配置
project(led_sync_system 
    VERSION 1.0.0
    DESCRIPTION "TV Background LED Sync System based on HPM5321"
    LANGUAGES C CXX ASM
)

# 编译器配置
set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 17)

# HPM5321 SDK路径 (需要根据实际SDK路径修改)
set(HPM_SDK_BASE ${CMAKE_CURRENT_SOURCE_DIR}/sdk/hpm_sdk)

# 包含HPM SDK配置
if(EXISTS ${HPM_SDK_BASE})
    include(${HPM_SDK_BASE}/cmake/toolchain.cmake)
else()
    message(WARNING "HPM SDK not found at ${HPM_SDK_BASE}")
endif()

# 编译选项
add_compile_options(
    -Wall
    -Wextra
    -O2
    -g
    -ffunction-sections
    -fdata-sections
)

# 链接选项
add_link_options(
    -Wl,--gc-sections
    -Wl,--print-memory-usage
)

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CMAKE_CURRENT_SOURCE_DIR}/src/drivers
    ${CMAKE_CURRENT_SOURCE_DIR}/src/algorithm
    ${CMAKE_CURRENT_SOURCE_DIR}/src/application
    ${CMAKE_CURRENT_SOURCE_DIR}/src/common
    ${CMAKE_CURRENT_SOURCE_DIR}/config
)

# 源文件
file(GLOB_RECURSE SOURCES
    "src/*.c"
    "src/*.cpp"
    "src/*.s"
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES})

# 链接库
target_link_libraries(${PROJECT_NAME}
    # HPM SDK库会在这里添加
)

# 生成hex和bin文件
add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_OBJCOPY} -O ihex $<TARGET_FILE:${PROJECT_NAME}> ${PROJECT_NAME}.hex
    COMMAND ${CMAKE_OBJCOPY} -O binary $<TARGET_FILE:${PROJECT_NAME}> ${PROJECT_NAME}.bin
    COMMENT "Generating hex and bin files"
)

# 显示内存使用情况
add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_SIZE} $<TARGET_FILE:${PROJECT_NAME}>
    COMMENT "Memory usage:"
)
