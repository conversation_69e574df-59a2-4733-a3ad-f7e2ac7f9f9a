/**
 * @file ble_driver.h
 * @brief 蓝牙BLE驱动接口定义
 * <AUTHOR> Sync System Team
 * @date 2025-07-25
 */

#ifndef BLE_DRIVER_H
#define BLE_DRIVER_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "system_config.h"

/* ========== BLE数据结构 ========== */

/**
 * @brief BLE设备地址
 */
typedef struct {
    uint8_t addr[6];        /**< MAC地址 */
    uint8_t type;           /**< 地址类型 */
} ble_addr_t;

/**
 * @brief BLE连接参数
 */
typedef struct {
    uint16_t conn_interval_min;     /**< 最小连接间隔 */
    uint16_t conn_interval_max;     /**< 最大连接间隔 */
    uint16_t slave_latency;         /**< 从设备延迟 */
    uint16_t supervision_timeout;   /**< 监督超时 */
} ble_conn_params_t;

/**
 * @brief BLE广播参数
 */
typedef struct {
    uint16_t adv_interval_min;      /**< 最小广播间隔 */
    uint16_t adv_interval_max;      /**< 最大广播间隔 */
    uint8_t adv_type;               /**< 广播类型 */
    uint8_t adv_filter_policy;      /**< 广播过滤策略 */
} ble_adv_params_t;

/**
 * @brief BLE状态
 */
typedef enum {
    BLE_STATE_UNINIT = 0,       /**< 未初始化 */
    BLE_STATE_INIT,             /**< 已初始化 */
    BLE_STATE_ADVERTISING,      /**< 广播中 */
    BLE_STATE_CONNECTED,        /**< 已连接 */
    BLE_STATE_DISCONNECTED,     /**< 已断开 */
    BLE_STATE_ERROR             /**< 错误状态 */
} ble_state_t;

/**
 * @brief BLE事件类型
 */
typedef enum {
    BLE_EVENT_CONNECTED = 0,    /**< 连接事件 */
    BLE_EVENT_DISCONNECTED,     /**< 断开事件 */
    BLE_EVENT_DATA_RECEIVED,    /**< 数据接收事件 */
    BLE_EVENT_DATA_SENT,        /**< 数据发送事件 */
    BLE_EVENT_MTU_CHANGED,      /**< MTU改变事件 */
    BLE_EVENT_ERROR             /**< 错误事件 */
} ble_event_type_t;

/**
 * @brief BLE事件数据
 */
typedef struct {
    ble_event_type_t type;      /**< 事件类型 */
    uint16_t conn_handle;       /**< 连接句柄 */
    uint16_t length;            /**< 数据长度 */
    uint8_t* data;              /**< 数据指针 */
    int32_t result;             /**< 结果码 */
} ble_event_t;

/**
 * @brief BLE事件回调函数类型
 */
typedef void (*ble_event_callback_t)(const ble_event_t* event);

/* ========== BLE服务和特征定义 ========== */

/**
 * @brief LED同步服务命令类型
 */
typedef enum {
    BLE_CMD_GET_STATUS = 0x01,      /**< 获取状态 */
    BLE_CMD_SET_CONFIG = 0x02,      /**< 设置配置 */
    BLE_CMD_GET_CONFIG = 0x03,      /**< 获取配置 */
    BLE_CMD_SET_COLOR = 0x04,       /**< 设置颜色 */
    BLE_CMD_SET_BRIGHTNESS = 0x05,  /**< 设置亮度 */
    BLE_CMD_START_SYNC = 0x06,      /**< 开始同步 */
    BLE_CMD_STOP_SYNC = 0x07,       /**< 停止同步 */
    BLE_CMD_SET_EFFECT = 0x08,      /**< 设置效果 */
    BLE_CMD_FIRMWARE_UPDATE = 0x09, /**< 固件升级 */
    BLE_CMD_RESET = 0x0A            /**< 系统重置 */
} ble_command_t;

/**
 * @brief BLE数据包结构
 */
typedef struct {
    uint8_t command;            /**< 命令类型 */
    uint8_t length;             /**< 数据长度 */
    uint8_t data[32];           /**< 数据内容 */
    uint8_t checksum;           /**< 校验和 */
} ble_packet_t;

/**
 * @brief 系统状态数据
 */
typedef struct {
    uint8_t system_state;       /**< 系统状态 */
    uint8_t sync_enabled;       /**< 同步使能 */
    uint8_t led_count;          /**< LED数量 */
    uint8_t brightness;         /**< 亮度 */
    uint16_t fps;               /**< 当前FPS */
    uint32_t uptime;            /**< 运行时间 */
    uint16_t free_memory;       /**< 空闲内存 */
} ble_status_data_t;

/* ========== BLE驱动接口 ========== */

/**
 * @brief BLE驱动初始化
 * @return 0: 成功, 负数: 错误码
 */
int ble_init(void);

/**
 * @brief BLE驱动去初始化
 */
void ble_deinit(void);

/**
 * @brief 开始BLE广播
 * @return 0: 成功, 负数: 错误码
 */
int ble_start_advertising(void);

/**
 * @brief 停止BLE广播
 * @return 0: 成功, 负数: 错误码
 */
int ble_stop_advertising(void);

/**
 * @brief 断开BLE连接
 * @return 0: 成功, 负数: 错误码
 */
int ble_disconnect(void);

/**
 * @brief 获取BLE状态
 * @return BLE状态
 */
ble_state_t ble_get_state(void);

/**
 * @brief 检查是否已连接
 * @return true: 已连接, false: 未连接
 */
bool ble_is_connected(void);

/**
 * @brief 设置事件回调函数
 * @param callback 回调函数指针
 */
void ble_set_event_callback(ble_event_callback_t callback);

/**
 * @brief BLE事件处理 (在主循环中调用)
 */
void ble_process(void);

/* ========== BLE数据传输接口 ========== */

/**
 * @brief 发送数据
 * @param data 数据指针
 * @param length 数据长度
 * @return 0: 成功, 负数: 错误码
 */
int ble_send_data(const uint8_t* data, uint16_t length);

/**
 * @brief 发送数据包
 * @param packet 数据包指针
 * @return 0: 成功, 负数: 错误码
 */
int ble_send_packet(const ble_packet_t* packet);

/**
 * @brief 发送状态数据
 * @param status 状态数据
 * @return 0: 成功, 负数: 错误码
 */
int ble_send_status(const ble_status_data_t* status);

/**
 * @brief 发送响应
 * @param command 命令类型
 * @param result 结果码
 * @param data 响应数据
 * @param length 数据长度
 * @return 0: 成功, 负数: 错误码
 */
int ble_send_response(uint8_t command, int32_t result, 
                      const uint8_t* data, uint8_t length);

/* ========== BLE配置接口 ========== */

/**
 * @brief 设置设备名称
 * @param name 设备名称
 * @return 0: 成功, 负数: 错误码
 */
int ble_set_device_name(const char* name);

/**
 * @brief 获取设备名称
 * @param name 设备名称输出缓冲区
 * @param max_length 缓冲区最大长度
 * @return 0: 成功, 负数: 错误码
 */
int ble_get_device_name(char* name, uint8_t max_length);

/**
 * @brief 设置连接参数
 * @param params 连接参数
 * @return 0: 成功, 负数: 错误码
 */
int ble_set_conn_params(const ble_conn_params_t* params);

/**
 * @brief 设置广播参数
 * @param params 广播参数
 * @return 0: 成功, 负数: 错误码
 */
int ble_set_adv_params(const ble_adv_params_t* params);

/**
 * @brief 获取本地MAC地址
 * @param addr MAC地址输出
 * @return 0: 成功, 负数: 错误码
 */
int ble_get_local_addr(ble_addr_t* addr);

/**
 * @brief 获取连接的设备地址
 * @param addr 设备地址输出
 * @return 0: 成功, 负数: 错误码
 */
int ble_get_peer_addr(ble_addr_t* addr);

/* ========== BLE工具函数 ========== */

/**
 * @brief 计算数据包校验和
 * @param packet 数据包指针
 * @return 校验和
 */
uint8_t ble_calculate_checksum(const ble_packet_t* packet);

/**
 * @brief 验证数据包校验和
 * @param packet 数据包指针
 * @return true: 校验正确, false: 校验错误
 */
bool ble_verify_checksum(const ble_packet_t* packet);

/**
 * @brief 解析接收到的数据包
 * @param data 原始数据
 * @param length 数据长度
 * @param packet 解析后的数据包
 * @return 0: 成功, 负数: 错误码
 */
int ble_parse_packet(const uint8_t* data, uint16_t length, ble_packet_t* packet);

/* ========== 错误码定义 ========== */
#define BLE_OK                  0
#define BLE_ERROR              -1
#define BLE_ERROR_PARAM        -2
#define BLE_ERROR_MEMORY       -3
#define BLE_ERROR_TIMEOUT      -4
#define BLE_ERROR_BUSY         -5
#define BLE_ERROR_NOT_READY    -6
#define BLE_ERROR_NOT_SUPPORT  -7
#define BLE_ERROR_NOT_CONNECTED -8
#define BLE_ERROR_CHECKSUM     -9

#ifdef __cplusplus
}
#endif

#endif /* BLE_DRIVER_H */
