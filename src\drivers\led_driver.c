/**
 * @file led_driver.c
 * @brief LED驱动实现
 * <AUTHOR> Sync System Team
 * @date 2025-07-25
 */

#include "led_driver.h"
#include "common/debug.h"
#include "common/system_init.h"
#include <string.h>
#include <stdlib.h>
#include <math.h>

/* ========== 私有数据结构 ========== */

/**
 * @brief LED驱动上下文
 */
typedef struct {
    led_config_t config;                /**< LED配置 */
    led_state_t state;                  /**< LED状态 */
    led_rgb_t* led_buffer;              /**< LED颜色缓冲区 */
    uint8_t* spi_buffer;                /**< SPI发送缓冲区 */
    uint32_t spi_buffer_size;           /**< SPI缓冲区大小 */
    led_effect_params_t current_effect; /**< 当前效果参数 */
    bool effect_running;                /**< 效果运行标志 */
    uint32_t effect_start_time;         /**< 效果开始时间 */
    uint32_t last_update_time;          /**< 最后更新时间 */
    bool update_pending;                /**< 更新待处理标志 */
} led_driver_context_t;

/* ========== 私有变量 ========== */
static led_driver_context_t g_led_ctx = {0};

/* WS2812B时序参数 (基于3.2MHz SPI时钟) */
#define WS2812B_T0H_BITS    0xC0    /* 0码高电平: ~0.4us */
#define WS2812B_T0L_BITS    0x00    /* 0码低电平: ~0.85us */
#define WS2812B_T1H_BITS    0xF8    /* 1码高电平: ~0.8us */
#define WS2812B_T1L_BITS    0x00    /* 1码低电平: ~0.45us */

/* 伽马校正表 */
static const uint8_t gamma_table[256] = {
    0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
    0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   1,   1,   1,   1,
    1,   1,   1,   1,   1,   1,   1,   1,   1,   2,   2,   2,   2,   2,   2,   2,
    2,   3,   3,   3,   3,   3,   3,   3,   4,   4,   4,   4,   4,   5,   5,   5,
    5,   6,   6,   6,   6,   7,   7,   7,   7,   8,   8,   8,   9,   9,   9,   10,
    10,  10,  11,  11,  11,  12,  12,  13,  13,  13,  14,  14,  15,  15,  16,  16,
    17,  17,  18,  18,  19,  19,  20,  20,  21,  21,  22,  22,  23,  24,  24,  25,
    25,  26,  27,  27,  28,  29,  29,  30,  31,  32,  32,  33,  34,  35,  35,  36,
    37,  38,  39,  39,  40,  41,  42,  43,  44,  45,  46,  47,  48,  49,  50,  50,
    51,  52,  54,  55,  56,  57,  58,  59,  60,  61,  62,  63,  64,  66,  67,  68,
    69,  70,  72,  73,  74,  75,  77,  78,  79,  81,  82,  83,  85,  86,  87,  89,
    90,  92,  93,  95,  96,  98,  99,  101, 102, 104, 105, 107, 109, 110, 112, 114,
    115, 117, 119, 120, 122, 124, 126, 127, 129, 131, 133, 135, 137, 138, 140, 142,
    144, 146, 148, 150, 152, 154, 156, 158, 160, 162, 164, 167, 169, 171, 173, 175,
    177, 180, 182, 184, 186, 189, 191, 193, 196, 198, 200, 203, 205, 208, 210, 213,
    215, 218, 220, 223, 225, 228, 231, 233, 236, 239, 241, 244, 247, 249, 252, 255
};

/* ========== 私有函数声明 ========== */
static int led_hw_init(void);
static int led_hw_deinit(void);
static int led_hw_send_data(const uint8_t* data, uint32_t size);
static int led_allocate_buffers(void);
static void led_free_buffers(void);
static void led_encode_ws2812b(const led_rgb_t* colors, uint16_t count, uint8_t* spi_data);
static void led_encode_pixel_ws2812b(led_rgb_t color, uint8_t* spi_data);
static void led_process_effects(void);
static led_rgb_t led_apply_brightness(led_rgb_t color, uint8_t brightness);

/* ========== 公共接口实现 ========== */

/**
 * @brief LED驱动初始化
 */
int led_init(void)
{
    DEBUG_ENTER();
    
    if (g_led_ctx.state != LED_STATE_UNINIT) {
        DEBUG_WARN("LED driver already initialized");
        return LED_OK;
    }
    
    /* 清零上下文 */
    memset(&g_led_ctx, 0, sizeof(led_driver_context_t));
    
    /* 设置默认配置 */
    g_led_ctx.config.led_count = LED_COUNT_DEFAULT;
    g_led_ctx.config.led_type = LED_TYPE;
    g_led_ctx.config.brightness = LED_BRIGHTNESS_DEFAULT;
    g_led_ctx.config.gamma_correction = true;
    g_led_ctx.config.color_order = 0; /* GRB for WS2812B */
    g_led_ctx.config.update_rate = 50; /* 50Hz */
    
    /* 硬件初始化 */
    int ret = led_hw_init();
    if (ret != LED_OK) {
        DEBUG_ERROR("LED hardware init failed: %d", ret);
        return ret;
    }
    
    /* 分配缓冲区 */
    ret = led_allocate_buffers();
    if (ret != LED_OK) {
        DEBUG_ERROR("LED buffer allocation failed: %d", ret);
        led_hw_deinit();
        return ret;
    }
    
    /* 清除所有LED */
    led_clear();
    led_update();
    
    g_led_ctx.state = LED_STATE_INIT;
    g_led_ctx.last_update_time = system_get_tick();
    
    DEBUG_INFO("LED driver initialized: %d LEDs, type=%d", 
               g_led_ctx.config.led_count, g_led_ctx.config.led_type);
    
    DEBUG_EXIT_RET(LED_OK);
    return LED_OK;
}

/**
 * @brief LED驱动去初始化
 */
void led_deinit(void)
{
    DEBUG_ENTER();
    
    if (g_led_ctx.state == LED_STATE_UNINIT) {
        DEBUG_WARN("LED driver not initialized");
        return;
    }
    
    /* 停止效果 */
    led_stop_effect();
    
    /* 清除所有LED */
    led_clear();
    led_update();
    
    /* 释放缓冲区 */
    led_free_buffers();
    
    /* 硬件去初始化 */
    led_hw_deinit();
    
    /* 清零上下文 */
    memset(&g_led_ctx, 0, sizeof(led_driver_context_t));
    g_led_ctx.state = LED_STATE_UNINIT;
    
    DEBUG_INFO("LED driver deinitialized");
    DEBUG_EXIT();
}

/**
 * @brief 设置LED配置
 */
int led_set_config(const led_config_t* config)
{
    if (config == NULL) {
        return LED_ERROR_PARAM;
    }
    
    DEBUG_ENTER();
    
    /* 验证参数 */
    if (config->led_count == 0 || config->led_count > LED_COUNT_MAX) {
        DEBUG_ERROR("Invalid LED count: %d", config->led_count);
        return LED_ERROR_PARAM;
    }
    
    if (config->brightness > LED_BRIGHTNESS_MAX) {
        DEBUG_ERROR("Invalid brightness: %d", config->brightness);
        return LED_ERROR_PARAM;
    }
    
    /* 保存配置 */
    bool need_realloc = (config->led_count != g_led_ctx.config.led_count);
    memcpy(&g_led_ctx.config, config, sizeof(led_config_t));
    
    /* 重新分配缓冲区 */
    if (need_realloc) {
        led_free_buffers();
        int ret = led_allocate_buffers();
        if (ret != LED_OK) {
            DEBUG_ERROR("Buffer reallocation failed: %d", ret);
            return ret;
        }
    }
    
    DEBUG_INFO("LED config updated: count=%d, brightness=%d", 
               config->led_count, config->brightness);
    
    DEBUG_EXIT_RET(LED_OK);
    return LED_OK;
}

/**
 * @brief 获取LED配置
 */
int led_get_config(led_config_t* config)
{
    if (config == NULL) {
        return LED_ERROR_PARAM;
    }
    
    memcpy(config, &g_led_ctx.config, sizeof(led_config_t));
    return LED_OK;
}

/**
 * @brief 更新LED显示
 */
int led_update(void)
{
    if (g_led_ctx.state == LED_STATE_UNINIT) {
        return LED_ERROR_NOT_READY;
    }
    
    DEBUG_ENTER();
    
    /* 处理效果 */
    if (g_led_ctx.effect_running) {
        led_process_effects();
    }
    
    /* 编码LED数据 */
    switch (g_led_ctx.config.led_type) {
        case LED_TYPE_WS2812B:
        case LED_TYPE_WS2811:
        case LED_TYPE_SK6812:
            led_encode_ws2812b(g_led_ctx.led_buffer, g_led_ctx.config.led_count, g_led_ctx.spi_buffer);
            break;
            
        default:
            DEBUG_ERROR("Unsupported LED type: %d", g_led_ctx.config.led_type);
            return LED_ERROR_NOT_SUPPORT;
    }
    
    /* 发送数据 */
    int ret = led_hw_send_data(g_led_ctx.spi_buffer, g_led_ctx.spi_buffer_size);
    if (ret != LED_OK) {
        DEBUG_ERROR("LED data send failed: %d", ret);
        return ret;
    }
    
    g_led_ctx.last_update_time = system_get_tick();
    g_led_ctx.update_pending = false;
    
    DEBUG_DEBUG("LED updated: %d LEDs", g_led_ctx.config.led_count);
    DEBUG_EXIT_RET(LED_OK);
    return LED_OK;
}

/**
 * @brief 获取LED状态
 */
led_state_t led_get_state(void)
{
    return g_led_ctx.state;
}

/* ========== LED颜色控制接口实现 ========== */

/**
 * @brief 设置单个LED颜色
 */
int led_set_pixel(uint16_t index, led_rgb_t color)
{
    if (g_led_ctx.state == LED_STATE_UNINIT) {
        return LED_ERROR_NOT_READY;
    }
    
    if (index >= g_led_ctx.config.led_count) {
        return LED_ERROR_INDEX;
    }
    
    /* 应用亮度和伽马校正 */
    color = led_apply_brightness(color, g_led_ctx.config.brightness);
    
    if (g_led_ctx.config.gamma_correction) {
        color = led_gamma_correct(color);
    }
    
    g_led_ctx.led_buffer[index] = color;
    g_led_ctx.update_pending = true;
    
    return LED_OK;
}

/**
 * @brief 获取单个LED颜色
 */
int led_get_pixel(uint16_t index, led_rgb_t* color)
{
    if (color == NULL) {
        return LED_ERROR_PARAM;
    }
    
    if (g_led_ctx.state == LED_STATE_UNINIT) {
        return LED_ERROR_NOT_READY;
    }
    
    if (index >= g_led_ctx.config.led_count) {
        return LED_ERROR_INDEX;
    }
    
    *color = g_led_ctx.led_buffer[index];
    return LED_OK;
}

/**
 * @brief 设置所有LED颜色
 */
int led_set_all(led_rgb_t color)
{
    if (g_led_ctx.state == LED_STATE_UNINIT) {
        return LED_ERROR_NOT_READY;
    }
    
    /* 应用亮度和伽马校正 */
    color = led_apply_brightness(color, g_led_ctx.config.brightness);
    
    if (g_led_ctx.config.gamma_correction) {
        color = led_gamma_correct(color);
    }
    
    for (uint16_t i = 0; i < g_led_ctx.config.led_count; i++) {
        g_led_ctx.led_buffer[i] = color;
    }
    
    g_led_ctx.update_pending = true;
    return LED_OK;
}

/**
 * @brief 清除所有LED
 */
int led_clear(void)
{
    led_rgb_t black = {0, 0, 0};
    return led_set_all(black);
}

/**
 * @brief 设置LED范围颜色
 */
int led_set_range(uint16_t start, uint16_t count, led_rgb_t color)
{
    if (g_led_ctx.state == LED_STATE_UNINIT) {
        return LED_ERROR_NOT_READY;
    }

    if (start >= g_led_ctx.config.led_count || (start + count) > g_led_ctx.config.led_count) {
        return LED_ERROR_INDEX;
    }

    /* 应用亮度和伽马校正 */
    color = led_apply_brightness(color, g_led_ctx.config.brightness);

    if (g_led_ctx.config.gamma_correction) {
        color = led_gamma_correct(color);
    }

    for (uint16_t i = start; i < start + count; i++) {
        g_led_ctx.led_buffer[i] = color;
    }

    g_led_ctx.update_pending = true;
    return LED_OK;
}

/**
 * @brief 批量设置LED颜色
 */
int led_set_buffer(const led_rgb_t* colors, uint16_t count)
{
    if (colors == NULL) {
        return LED_ERROR_PARAM;
    }

    if (g_led_ctx.state == LED_STATE_UNINIT) {
        return LED_ERROR_NOT_READY;
    }

    if (count > g_led_ctx.config.led_count) {
        count = g_led_ctx.config.led_count;
    }

    for (uint16_t i = 0; i < count; i++) {
        led_rgb_t color = colors[i];

        /* 应用亮度和伽马校正 */
        color = led_apply_brightness(color, g_led_ctx.config.brightness);

        if (g_led_ctx.config.gamma_correction) {
            color = led_gamma_correct(color);
        }

        g_led_ctx.led_buffer[i] = color;
    }

    g_led_ctx.update_pending = true;
    return LED_OK;
}

/* ========== LED亮度控制接口实现 ========== */

/**
 * @brief 设置全局亮度
 */
int led_set_brightness(uint8_t brightness)
{
    if (brightness > LED_BRIGHTNESS_MAX) {
        return LED_ERROR_PARAM;
    }

    g_led_ctx.config.brightness = brightness;
    g_led_ctx.update_pending = true;

    DEBUG_INFO("LED brightness set to: %d", brightness);
    return LED_OK;
}

/**
 * @brief 获取全局亮度
 */
uint8_t led_get_brightness(void)
{
    return g_led_ctx.config.brightness;
}

/**
 * @brief 渐变到指定亮度
 */
int led_fade_brightness(uint8_t target_brightness, uint32_t duration)
{
    if (target_brightness > LED_BRIGHTNESS_MAX) {
        return LED_ERROR_PARAM;
    }

    /* 启动亮度渐变效果 */
    led_effect_params_t effect = {0};
    effect.type = LED_EFFECT_FADE;
    effect.duration = duration;
    effect.color1.red = g_led_ctx.config.brightness;
    effect.color2.red = target_brightness;

    return led_start_effect(&effect);
}

/* ========== LED效果控制接口实现 ========== */

/**
 * @brief 启动LED效果
 */
int led_start_effect(const led_effect_params_t* params)
{
    if (params == NULL) {
        return LED_ERROR_PARAM;
    }

    if (g_led_ctx.state == LED_STATE_UNINIT) {
        return LED_ERROR_NOT_READY;
    }

    /* 保存效果参数 */
    memcpy(&g_led_ctx.current_effect, params, sizeof(led_effect_params_t));

    g_led_ctx.effect_running = true;
    g_led_ctx.effect_start_time = system_get_tick();
    g_led_ctx.state = LED_STATE_RUNNING;

    DEBUG_INFO("LED effect started: type=%d, duration=%u ms",
               params->type, params->duration);

    return LED_OK;
}

/**
 * @brief 停止LED效果
 */
int led_stop_effect(void)
{
    if (g_led_ctx.effect_running) {
        g_led_ctx.effect_running = false;
        g_led_ctx.state = LED_STATE_INIT;

        DEBUG_INFO("LED effect stopped");
    }

    return LED_OK;
}

/**
 * @brief 暂停LED效果
 */
int led_pause_effect(void)
{
    if (g_led_ctx.effect_running) {
        g_led_ctx.state = LED_STATE_PAUSED;
        DEBUG_INFO("LED effect paused");
    }

    return LED_OK;
}

/**
 * @brief 恢复LED效果
 */
int led_resume_effect(void)
{
    if (g_led_ctx.effect_running && g_led_ctx.state == LED_STATE_PAUSED) {
        g_led_ctx.state = LED_STATE_RUNNING;
        DEBUG_INFO("LED effect resumed");
    }

    return LED_OK;
}

/**
 * @brief 检查效果是否运行中
 */
bool led_is_effect_running(void)
{
    return g_led_ctx.effect_running;
}

/* ========== 颜色转换工具函数实现 ========== */

/**
 * @brief HSV转RGB
 */
led_rgb_t led_hsv_to_rgb(led_hsv_t hsv)
{
    led_rgb_t rgb = {0};

    float h = hsv.hue / 60.0f;
    float s = hsv.saturation / 255.0f;
    float v = hsv.value / 255.0f;

    int i = (int)h;
    float f = h - i;
    float p = v * (1.0f - s);
    float q = v * (1.0f - s * f);
    float t = v * (1.0f - s * (1.0f - f));

    switch (i % 6) {
        case 0: rgb.red = v * 255; rgb.green = t * 255; rgb.blue = p * 255; break;
        case 1: rgb.red = q * 255; rgb.green = v * 255; rgb.blue = p * 255; break;
        case 2: rgb.red = p * 255; rgb.green = v * 255; rgb.blue = t * 255; break;
        case 3: rgb.red = p * 255; rgb.green = q * 255; rgb.blue = v * 255; break;
        case 4: rgb.red = t * 255; rgb.green = p * 255; rgb.blue = v * 255; break;
        case 5: rgb.red = v * 255; rgb.green = p * 255; rgb.blue = q * 255; break;
    }

    return rgb;
}

/**
 * @brief RGB转HSV
 */
led_hsv_t led_rgb_to_hsv(led_rgb_t rgb)
{
    led_hsv_t hsv = {0};

    float r = rgb.red / 255.0f;
    float g = rgb.green / 255.0f;
    float b = rgb.blue / 255.0f;

    float max_val = fmaxf(fmaxf(r, g), b);
    float min_val = fminf(fminf(r, g), b);
    float diff = max_val - min_val;

    /* Value */
    hsv.value = max_val * 255;

    /* Saturation */
    if (max_val != 0) {
        hsv.saturation = (diff / max_val) * 255;
    }

    /* Hue */
    if (diff != 0) {
        if (max_val == r) {
            hsv.hue = 60 * ((g - b) / diff);
        } else if (max_val == g) {
            hsv.hue = 60 * (2 + (b - r) / diff);
        } else {
            hsv.hue = 60 * (4 + (r - g) / diff);
        }

        if (hsv.hue < 0) {
            hsv.hue += 360;
        }
    }

    return hsv;
}

/**
 * @brief 伽马校正
 */
led_rgb_t led_gamma_correct(led_rgb_t color)
{
    led_rgb_t result;
    result.red = gamma_table[color.red];
    result.green = gamma_table[color.green];
    result.blue = gamma_table[color.blue];
    return result;
}

/* ========== 私有函数实现 ========== */

/**
 * @brief 硬件初始化
 */
static int led_hw_init(void)
{
    DEBUG_ENTER();

    /* TODO: 实现具体的硬件初始化 */
    /* 初始化SPI接口 */
    /* 配置GPIO引脚 */
    /* 设置SPI参数 */

    DEBUG_INFO("LED hardware initialized: SPI%d, baudrate=%u",
               LED_SPI_INSTANCE, LED_SPI_BAUDRATE);

    DEBUG_EXIT_RET(LED_OK);
    return LED_OK;
}

/**
 * @brief 硬件去初始化
 */
static int led_hw_deinit(void)
{
    DEBUG_ENTER();

    /* TODO: 实现具体的硬件去初始化 */

    DEBUG_EXIT_RET(LED_OK);
    return LED_OK;
}

/**
 * @brief 发送数据到LED
 */
static int led_hw_send_data(const uint8_t* data, uint32_t size)
{
    if (data == NULL || size == 0) {
        return LED_ERROR_PARAM;
    }

    /* TODO: 实现具体的SPI数据发送 */
    /* 使用DMA发送数据 */
    /* 等待发送完成 */

    DEBUG_DEBUG("LED data sent: %u bytes", size);
    return LED_OK;
}

/**
 * @brief 分配缓冲区
 */
static int led_allocate_buffers(void)
{
    DEBUG_ENTER();

    /* 分配LED颜色缓冲区 */
    uint32_t led_buffer_size = g_led_ctx.config.led_count * sizeof(led_rgb_t);
    g_led_ctx.led_buffer = (led_rgb_t*)system_malloc(led_buffer_size);
    if (g_led_ctx.led_buffer == NULL) {
        DEBUG_ERROR("Failed to allocate LED buffer");
        return LED_ERROR_MEMORY;
    }

    /* 分配SPI发送缓冲区 (WS2812B: 每个LED需要24位 * 4字节/位 + 复位时间) */
    g_led_ctx.spi_buffer_size = g_led_ctx.config.led_count * 24 * 4 + 100;
    g_led_ctx.spi_buffer = (uint8_t*)system_malloc(g_led_ctx.spi_buffer_size);
    if (g_led_ctx.spi_buffer == NULL) {
        DEBUG_ERROR("Failed to allocate SPI buffer");
        system_free(g_led_ctx.led_buffer);
        return LED_ERROR_MEMORY;
    }

    /* 清零缓冲区 */
    memset(g_led_ctx.led_buffer, 0, led_buffer_size);
    memset(g_led_ctx.spi_buffer, 0, g_led_ctx.spi_buffer_size);

    DEBUG_INFO("LED buffers allocated: LED=%u bytes, SPI=%u bytes",
               led_buffer_size, g_led_ctx.spi_buffer_size);

    DEBUG_EXIT_RET(LED_OK);
    return LED_OK;
}

/**
 * @brief 释放缓冲区
 */
static void led_free_buffers(void)
{
    DEBUG_ENTER();

    if (g_led_ctx.led_buffer != NULL) {
        system_free(g_led_ctx.led_buffer);
        g_led_ctx.led_buffer = NULL;
    }

    if (g_led_ctx.spi_buffer != NULL) {
        system_free(g_led_ctx.spi_buffer);
        g_led_ctx.spi_buffer = NULL;
    }

    DEBUG_INFO("LED buffers freed");
    DEBUG_EXIT();
}

/**
 * @brief WS2812B编码
 */
static void led_encode_ws2812b(const led_rgb_t* colors, uint16_t count, uint8_t* spi_data)
{
    uint32_t spi_index = 0;

    /* 编码每个LED */
    for (uint16_t i = 0; i < count; i++) {
        led_encode_pixel_ws2812b(colors[i], &spi_data[spi_index]);
        spi_index += 24 * 4; /* 每个像素24位，每位4字节 */
    }

    /* 添加复位时间 (至少50us的低电平) */
    for (uint32_t i = 0; i < 100; i++) {
        spi_data[spi_index++] = 0x00;
    }
}

/**
 * @brief 单个像素WS2812B编码
 */
static void led_encode_pixel_ws2812b(led_rgb_t color, uint8_t* spi_data)
{
    /* WS2812B颜色顺序: GRB */
    uint8_t colors[3] = {color.green, color.red, color.blue};
    uint32_t spi_index = 0;

    for (int i = 0; i < 3; i++) {
        uint8_t byte = colors[i];

        /* 编码8位数据 */
        for (int bit = 7; bit >= 0; bit--) {
            if (byte & (1 << bit)) {
                /* 发送'1' */
                spi_data[spi_index++] = WS2812B_T1H_BITS;
                spi_data[spi_index++] = WS2812B_T1H_BITS;
                spi_data[spi_index++] = WS2812B_T1L_BITS;
                spi_data[spi_index++] = WS2812B_T1L_BITS;
            } else {
                /* 发送'0' */
                spi_data[spi_index++] = WS2812B_T0H_BITS;
                spi_data[spi_index++] = WS2812B_T0L_BITS;
                spi_data[spi_index++] = WS2812B_T0L_BITS;
                spi_data[spi_index++] = WS2812B_T0L_BITS;
            }
        }
    }
}

/**
 * @brief 处理LED效果
 */
static void led_process_effects(void)
{
    if (!g_led_ctx.effect_running || g_led_ctx.state == LED_STATE_PAUSED) {
        return;
    }

    uint32_t current_time = system_get_tick();
    uint32_t elapsed_time = current_time - g_led_ctx.effect_start_time;

    /* 检查效果是否结束 */
    if (g_led_ctx.current_effect.duration > 0 && elapsed_time >= g_led_ctx.current_effect.duration) {
        led_stop_effect();
        return;
    }

    switch (g_led_ctx.current_effect.type) {
        case LED_EFFECT_STATIC:
            led_set_all(g_led_ctx.current_effect.color1);
            break;

        case LED_EFFECT_BREATHING:
            {
                float phase = (float)(elapsed_time % g_led_ctx.current_effect.period) / g_led_ctx.current_effect.period;
                float brightness = (sinf(phase * 2 * M_PI) + 1.0f) / 2.0f;

                led_rgb_t color = g_led_ctx.current_effect.color1;
                color.red = (uint8_t)(color.red * brightness);
                color.green = (uint8_t)(color.green * brightness);
                color.blue = (uint8_t)(color.blue * brightness);

                led_set_all(color);
            }
            break;

        case LED_EFFECT_RAINBOW:
            {
                float hue_step = 360.0f / g_led_ctx.config.led_count;
                float time_offset = (float)(elapsed_time % g_led_ctx.current_effect.period) /
                                   g_led_ctx.current_effect.period * 360.0f;

                for (uint16_t i = 0; i < g_led_ctx.config.led_count; i++) {
                    led_hsv_t hsv = {
                        .hue = (uint16_t)((i * hue_step + time_offset)) % 360,
                        .saturation = 255,
                        .value = g_led_ctx.current_effect.color1.red /* 使用red作为亮度 */
                    };

                    led_rgb_t rgb = led_hsv_to_rgb(hsv);
                    led_set_pixel(i, rgb);
                }
            }
            break;

        case LED_EFFECT_CHASE:
            {
                uint32_t position = (elapsed_time / (g_led_ctx.current_effect.period / g_led_ctx.config.led_count))
                                   % g_led_ctx.config.led_count;

                led_clear();
                led_set_pixel(position, g_led_ctx.current_effect.color1);

                /* 添加尾迹效果 */
                for (int i = 1; i <= 3; i++) {
                    uint16_t tail_pos = (position - i + g_led_ctx.config.led_count) % g_led_ctx.config.led_count;
                    led_rgb_t tail_color = g_led_ctx.current_effect.color1;
                    tail_color.red /= (i + 1);
                    tail_color.green /= (i + 1);
                    tail_color.blue /= (i + 1);
                    led_set_pixel(tail_pos, tail_color);
                }
            }
            break;

        case LED_EFFECT_FADE:
            {
                float progress = (float)elapsed_time / g_led_ctx.current_effect.duration;
                if (progress > 1.0f) progress = 1.0f;

                /* 在这里实现亮度渐变 */
                uint8_t start_brightness = g_led_ctx.current_effect.color1.red;
                uint8_t end_brightness = g_led_ctx.current_effect.color2.red;
                uint8_t current_brightness = start_brightness +
                    (uint8_t)((end_brightness - start_brightness) * progress);

                led_set_brightness(current_brightness);
            }
            break;

        default:
            break;
    }
}

/**
 * @brief 应用亮度
 */
static led_rgb_t led_apply_brightness(led_rgb_t color, uint8_t brightness)
{
    led_rgb_t result;

    result.red = (color.red * brightness) / 255;
    result.green = (color.green * brightness) / 255;
    result.blue = (color.blue * brightness) / 255;

    return result;
}
