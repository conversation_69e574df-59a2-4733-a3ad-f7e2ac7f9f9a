/**
 * @file sync_engine.c
 * @brief LED同步引擎实现
 * <AUTHOR> Sync System Team
 * @date 2025-07-25
 */

#include "sync_engine.h"
#include "common/debug.h"
#include "common/system_init.h"
#include <string.h>
#include <stdlib.h>
#include <math.h>

/* ========== 私有数据结构 ========== */

/**
 * @brief 同步引擎上下文
 */
typedef struct {
    sync_engine_config_t config;        /**< 配置参数 */
    sync_engine_state_t state;          /**< 引擎状态 */
    sync_engine_stats_t stats;          /**< 统计信息 */
    led_region_map_t* region_maps;      /**< 区域映射 */
    uint8_t region_count;               /**< 区域数量 */
    led_rgb_t* led_colors;              /**< LED颜色缓冲区 */
    uint32_t last_update_time;          /**< 最后更新时间 */
    uint32_t last_color_change_time;    /**< 最后颜色变化时间 */
    led_rgb_t last_dominant_color;      /**< 最后的主色调 */
    bool initialized;                   /**< 初始化标志 */
} sync_engine_context_t;

/* ========== 私有变量 ========== */
static sync_engine_context_t g_sync_ctx = {0};

/* ========== 私有函数声明 ========== */
static int sync_engine_validate_config(const sync_engine_config_t* config);
static void sync_engine_update_stats(uint32_t process_time);
static void sync_engine_map_colors_to_leds(const color_extract_result_t* result);
static void sync_engine_apply_linear_mapping(const color_extract_result_t* result);
static void sync_engine_apply_circular_mapping(const color_extract_result_t* result);
static void sync_engine_apply_region_mapping(const color_extract_result_t* result);
static led_rgb_t sync_engine_adjust_color(led_rgb_t color);
static void sync_engine_handle_ble_event(const ble_event_t* event);
static int sync_engine_process_ble_command(const ble_packet_t* packet);

/* ========== 公共接口实现 ========== */

/**
 * @brief 同步引擎初始化
 */
int sync_engine_init(void)
{
    DEBUG_ENTER();
    
    if (g_sync_ctx.initialized) {
        DEBUG_WARN("Sync engine already initialized");
        return SYNC_ENGINE_OK;
    }
    
    /* 清零上下文 */
    memset(&g_sync_ctx, 0, sizeof(sync_engine_context_t));
    
    /* 设置默认配置 */
    g_sync_ctx.config.sync_mode = SYNC_MODE_FULL_SCREEN;
    g_sync_ctx.config.led_mapping = LED_MAP_LINEAR;
    g_sync_ctx.config.led_count = LED_COUNT_DEFAULT;
    g_sync_ctx.config.brightness = LED_BRIGHTNESS_DEFAULT;
    g_sync_ctx.config.saturation_boost = 0;
    g_sync_ctx.config.color_temperature = 128;
    g_sync_ctx.config.auto_brightness = false;
    g_sync_ctx.config.smooth_transition = true;
    g_sync_ctx.config.transition_time = COLOR_TRANSITION_TIME;
    g_sync_ctx.config.update_rate = 30;
    
    /* 设置颜色提取配置 */
    g_sync_ctx.config.color_config.method = COLOR_EXTRACT_METHOD;
    g_sync_ctx.config.color_config.cluster_count = COLOR_CLUSTER_COUNT;
    g_sync_ctx.config.color_config.sample_rate = COLOR_SAMPLE_RATE;
    g_sync_ctx.config.color_config.edge_detection = EDGE_DETECT_ENABLED;
    g_sync_ctx.config.color_config.edge_threshold = EDGE_THRESHOLD;
    g_sync_ctx.config.color_config.color_smooth = COLOR_SMOOTH_ENABLED;
    g_sync_ctx.config.color_config.smooth_factor = COLOR_SMOOTH_FACTOR;
    g_sync_ctx.config.color_config.region_count = 4;
    g_sync_ctx.config.color_config.brightness_adapt = true;
    
    /* 分配LED颜色缓冲区 */
    g_sync_ctx.led_colors = (led_rgb_t*)system_malloc(LED_COUNT_MAX * sizeof(led_rgb_t));
    if (g_sync_ctx.led_colors == NULL) {
        DEBUG_ERROR("Failed to allocate LED color buffer");
        return SYNC_ENGINE_ERROR_MEMORY;
    }
    
    /* 分配区域映射缓冲区 */
    g_sync_ctx.region_maps = (led_region_map_t*)system_malloc(16 * sizeof(led_region_map_t));
    if (g_sync_ctx.region_maps == NULL) {
        DEBUG_ERROR("Failed to allocate region map buffer");
        system_free(g_sync_ctx.led_colors);
        return SYNC_ENGINE_ERROR_MEMORY;
    }
    
    /* 设置BLE事件回调 */
    ble_set_event_callback(sync_engine_handle_ble_event);
    
    /* 初始化统计信息 */
    g_sync_ctx.stats.min_process_time = UINT32_MAX;
    
    g_sync_ctx.state = SYNC_ENGINE_STATE_INIT;
    g_sync_ctx.initialized = true;
    
    DEBUG_INFO("Sync engine initialized successfully");
    DEBUG_EXIT_RET(SYNC_ENGINE_OK);
    return SYNC_ENGINE_OK;
}

/**
 * @brief 同步引擎去初始化
 */
void sync_engine_deinit(void)
{
    DEBUG_ENTER();
    
    if (!g_sync_ctx.initialized) {
        DEBUG_WARN("Sync engine not initialized");
        return;
    }
    
    /* 停止同步引擎 */
    if (g_sync_ctx.state == SYNC_ENGINE_STATE_RUNNING) {
        sync_engine_stop();
    }
    
    /* 释放缓冲区 */
    if (g_sync_ctx.led_colors != NULL) {
        system_free(g_sync_ctx.led_colors);
        g_sync_ctx.led_colors = NULL;
    }
    
    if (g_sync_ctx.region_maps != NULL) {
        system_free(g_sync_ctx.region_maps);
        g_sync_ctx.region_maps = NULL;
    }
    
    /* 清零上下文 */
    memset(&g_sync_ctx, 0, sizeof(sync_engine_context_t));
    
    DEBUG_INFO("Sync engine deinitialized");
    DEBUG_EXIT();
}

/**
 * @brief 启动同步引擎
 */
int sync_engine_start(void)
{
    DEBUG_ENTER();
    
    if (!g_sync_ctx.initialized) {
        return SYNC_ENGINE_ERROR_NOT_READY;
    }
    
    if (g_sync_ctx.state == SYNC_ENGINE_STATE_RUNNING) {
        DEBUG_WARN("Sync engine already running");
        return SYNC_ENGINE_OK;
    }
    
    /* 启动摄像头 */
    int ret = camera_start_streaming();
    if (ret != CAMERA_OK) {
        DEBUG_ERROR("Failed to start camera: %d", ret);
        return SYNC_ENGINE_ERROR;
    }
    
    /* 启动BLE广播 */
    ret = ble_start_advertising();
    if (ret != BLE_OK) {
        DEBUG_ERROR("Failed to start BLE advertising: %d", ret);
        camera_stop_streaming();
        return SYNC_ENGINE_ERROR;
    }
    
    g_sync_ctx.state = SYNC_ENGINE_STATE_RUNNING;
    g_sync_ctx.last_update_time = system_get_tick();
    
    DEBUG_INFO("Sync engine started");
    DEBUG_EXIT_RET(SYNC_ENGINE_OK);
    return SYNC_ENGINE_OK;
}

/**
 * @brief 停止同步引擎
 */
int sync_engine_stop(void)
{
    DEBUG_ENTER();
    
    if (g_sync_ctx.state != SYNC_ENGINE_STATE_RUNNING) {
        DEBUG_WARN("Sync engine not running");
        return SYNC_ENGINE_OK;
    }
    
    /* 停止摄像头 */
    camera_stop_streaming();
    
    /* 停止BLE广播 */
    ble_stop_advertising();
    
    /* 清除LED */
    led_clear();
    led_update();
    
    g_sync_ctx.state = SYNC_ENGINE_STATE_INIT;
    
    DEBUG_INFO("Sync engine stopped");
    DEBUG_EXIT_RET(SYNC_ENGINE_OK);
    return SYNC_ENGINE_OK;
}

/**
 * @brief 暂停同步引擎
 */
int sync_engine_pause(void)
{
    if (g_sync_ctx.state == SYNC_ENGINE_STATE_RUNNING) {
        g_sync_ctx.state = SYNC_ENGINE_STATE_PAUSED;
        DEBUG_INFO("Sync engine paused");
    }
    
    return SYNC_ENGINE_OK;
}

/**
 * @brief 恢复同步引擎
 */
int sync_engine_resume(void)
{
    if (g_sync_ctx.state == SYNC_ENGINE_STATE_PAUSED) {
        g_sync_ctx.state = SYNC_ENGINE_STATE_RUNNING;
        DEBUG_INFO("Sync engine resumed");
    }
    
    return SYNC_ENGINE_OK;
}

/**
 * @brief 处理摄像头帧数据
 */
int sync_engine_process_frame(const camera_frame_t* frame)
{
    if (frame == NULL) {
        return SYNC_ENGINE_ERROR_PARAM;
    }
    
    if (!g_sync_ctx.initialized || g_sync_ctx.state != SYNC_ENGINE_STATE_RUNNING) {
        return SYNC_ENGINE_ERROR_NOT_READY;
    }
    
    DEBUG_ENTER();
    
    uint32_t start_time = system_get_tick();
    
    /* 提取颜色 */
    color_extract_result_t color_result;
    int ret = color_extract_from_frame(frame, &color_result);
    if (ret != COLOR_EXTRACT_OK) {
        DEBUG_ERROR("Color extract failed: %d", ret);
        g_sync_ctx.stats.dropped_frames++;
        return SYNC_ENGINE_ERROR;
    }
    
    /* 检查颜色是否有显著变化 */
    float color_distance = color_calculate_distance(color_result.dominant_color, g_sync_ctx.last_dominant_color);
    if (color_distance > 30.0f) { /* 颜色变化阈值 */
        g_sync_ctx.stats.color_changes++;
        g_sync_ctx.last_color_change_time = start_time;
    }
    
    /* 映射颜色到LED */
    sync_engine_map_colors_to_leds(&color_result);
    
    /* 更新LED */
    ret = led_update();
    if (ret != LED_OK) {
        DEBUG_ERROR("LED update failed: %d", ret);
        return SYNC_ENGINE_ERROR;
    }
    
    /* 更新统计信息 */
    uint32_t process_time = system_get_tick() - start_time;
    sync_engine_update_stats(process_time);
    
    /* 保存最后的颜色 */
    g_sync_ctx.last_dominant_color = color_result.dominant_color;
    g_sync_ctx.last_update_time = start_time;
    
    DEBUG_DEBUG("Frame processed: R=%d G=%d B=%d, time=%u ms",
               color_result.dominant_color.red, color_result.dominant_color.green, 
               color_result.dominant_color.blue, process_time);
    
    DEBUG_EXIT_RET(SYNC_ENGINE_OK);
    return SYNC_ENGINE_OK;
}

/**
 * @brief 获取同步引擎状态
 */
sync_engine_state_t sync_engine_get_state(void)
{
    return g_sync_ctx.state;
}

/**
 * @brief 检查同步引擎是否运行
 */
bool sync_engine_is_running(void)
{
    return (g_sync_ctx.state == SYNC_ENGINE_STATE_RUNNING);
}

/* ========== 配置管理接口实现 ========== */

/**
 * @brief 设置同步引擎配置
 */
int sync_engine_set_config(const sync_engine_config_t* config)
{
    if (config == NULL) {
        return SYNC_ENGINE_ERROR_PARAM;
    }

    DEBUG_ENTER();

    /* 验证配置参数 */
    int ret = sync_engine_validate_config(config);
    if (ret != SYNC_ENGINE_OK) {
        DEBUG_ERROR("Invalid config: %d", ret);
        return ret;
    }

    /* 保存配置 */
    memcpy(&g_sync_ctx.config, config, sizeof(sync_engine_config_t));

    /* 更新颜色提取配置 */
    ret = color_extract_set_config(&g_sync_ctx.config.color_config);
    if (ret != COLOR_EXTRACT_OK) {
        DEBUG_ERROR("Failed to set color extract config: %d", ret);
        return SYNC_ENGINE_ERROR_CONFIG;
    }

    /* 更新LED配置 */
    led_config_t led_config;
    led_get_config(&led_config);
    led_config.led_count = g_sync_ctx.config.led_count;
    led_config.brightness = g_sync_ctx.config.brightness;
    led_config.update_rate = g_sync_ctx.config.update_rate;

    ret = led_set_config(&led_config);
    if (ret != LED_OK) {
        DEBUG_ERROR("Failed to set LED config: %d", ret);
        return SYNC_ENGINE_ERROR_CONFIG;
    }

    DEBUG_INFO("Sync engine config updated");
    DEBUG_EXIT_RET(SYNC_ENGINE_OK);
    return SYNC_ENGINE_OK;
}

/**
 * @brief 获取同步引擎配置
 */
int sync_engine_get_config(sync_engine_config_t* config)
{
    if (config == NULL) {
        return SYNC_ENGINE_ERROR_PARAM;
    }

    memcpy(config, &g_sync_ctx.config, sizeof(sync_engine_config_t));
    return SYNC_ENGINE_OK;
}

/**
 * @brief 加载默认配置
 */
int sync_engine_load_default_config(void)
{
    DEBUG_ENTER();

    sync_engine_config_t default_config = {0};

    /* 设置默认值 */
    default_config.sync_mode = SYNC_MODE_FULL_SCREEN;
    default_config.led_mapping = LED_MAP_LINEAR;
    default_config.led_count = LED_COUNT_DEFAULT;
    default_config.brightness = LED_BRIGHTNESS_DEFAULT;
    default_config.saturation_boost = 0;
    default_config.color_temperature = 128;
    default_config.auto_brightness = false;
    default_config.smooth_transition = true;
    default_config.transition_time = COLOR_TRANSITION_TIME;
    default_config.update_rate = 30;

    /* 颜色提取默认配置 */
    default_config.color_config.method = COLOR_EXTRACT_METHOD;
    default_config.color_config.cluster_count = COLOR_CLUSTER_COUNT;
    default_config.color_config.sample_rate = COLOR_SAMPLE_RATE;
    default_config.color_config.edge_detection = EDGE_DETECT_ENABLED;
    default_config.color_config.edge_threshold = EDGE_THRESHOLD;
    default_config.color_config.color_smooth = COLOR_SMOOTH_ENABLED;
    default_config.color_config.smooth_factor = COLOR_SMOOTH_FACTOR;
    default_config.color_config.region_count = 4;
    default_config.color_config.brightness_adapt = true;

    int ret = sync_engine_set_config(&default_config);

    DEBUG_INFO("Default config loaded");
    DEBUG_EXIT_RET(ret);
    return ret;
}

/**
 * @brief 设置同步模式
 */
int sync_engine_set_sync_mode(sync_mode_t mode)
{
    g_sync_ctx.config.sync_mode = mode;
    DEBUG_INFO("Sync mode set to: %d", mode);
    return SYNC_ENGINE_OK;
}

/**
 * @brief 获取同步模式
 */
sync_mode_t sync_engine_get_sync_mode(void)
{
    return g_sync_ctx.config.sync_mode;
}

/**
 * @brief 设置LED映射模式
 */
int sync_engine_set_led_mapping(led_mapping_t mapping)
{
    g_sync_ctx.config.led_mapping = mapping;
    DEBUG_INFO("LED mapping set to: %d", mapping);
    return SYNC_ENGINE_OK;
}

/**
 * @brief 设置自定义LED区域映射
 */
int sync_engine_set_custom_mapping(const led_region_map_t* maps, uint8_t count)
{
    if (maps == NULL || count == 0 || count > 16) {
        return SYNC_ENGINE_ERROR_PARAM;
    }

    /* 复制映射配置 */
    memcpy(g_sync_ctx.region_maps, maps, count * sizeof(led_region_map_t));
    g_sync_ctx.region_count = count;

    /* 设置为自定义映射模式 */
    g_sync_ctx.config.led_mapping = LED_MAP_CUSTOM;

    DEBUG_INFO("Custom LED mapping set: %d regions", count);
    return SYNC_ENGINE_OK;
}

/* ========== 参数调节接口实现 ========== */

/**
 * @brief 设置亮度
 */
int sync_engine_set_brightness(uint8_t brightness)
{
    if (brightness > LED_BRIGHTNESS_MAX) {
        return SYNC_ENGINE_ERROR_PARAM;
    }

    g_sync_ctx.config.brightness = brightness;

    /* 更新LED亮度 */
    led_set_brightness(brightness);

    DEBUG_INFO("Brightness set to: %d", brightness);
    return SYNC_ENGINE_OK;
}

/**
 * @brief 获取亮度
 */
uint8_t sync_engine_get_brightness(void)
{
    return g_sync_ctx.config.brightness;
}

/**
 * @brief 设置饱和度增强
 */
int sync_engine_set_saturation_boost(uint8_t boost)
{
    g_sync_ctx.config.saturation_boost = boost;
    DEBUG_INFO("Saturation boost set to: %d", boost);
    return SYNC_ENGINE_OK;
}

/**
 * @brief 设置色温
 */
int sync_engine_set_color_temperature(uint8_t temperature)
{
    g_sync_ctx.config.color_temperature = temperature;
    DEBUG_INFO("Color temperature set to: %d", temperature);
    return SYNC_ENGINE_OK;
}

/**
 * @brief 设置自动亮度
 */
int sync_engine_set_auto_brightness(bool enable)
{
    g_sync_ctx.config.auto_brightness = enable;
    DEBUG_INFO("Auto brightness: %s", enable ? "enabled" : "disabled");
    return SYNC_ENGINE_OK;
}

/**
 * @brief 设置平滑过渡
 */
int sync_engine_set_smooth_transition(bool enable, uint16_t transition_time)
{
    g_sync_ctx.config.smooth_transition = enable;
    g_sync_ctx.config.transition_time = transition_time;

    DEBUG_INFO("Smooth transition: %s, time=%d ms",
               enable ? "enabled" : "disabled", transition_time);
    return SYNC_ENGINE_OK;
}

/* ========== 统计信息接口实现 ========== */

/**
 * @brief 获取统计信息
 */
int sync_engine_get_stats(sync_engine_stats_t* stats)
{
    if (stats == NULL) {
        return SYNC_ENGINE_ERROR_PARAM;
    }

    memcpy(stats, &g_sync_ctx.stats, sizeof(sync_engine_stats_t));
    return SYNC_ENGINE_OK;
}

/**
 * @brief 重置统计信息
 */
void sync_engine_reset_stats(void)
{
    memset(&g_sync_ctx.stats, 0, sizeof(sync_engine_stats_t));
    g_sync_ctx.stats.min_process_time = UINT32_MAX;
    DEBUG_INFO("Sync engine stats reset");
}

/* ========== 私有函数实现 ========== */

/**
 * @brief 验证配置参数
 */
static int sync_engine_validate_config(const sync_engine_config_t* config)
{
    if (config->led_count == 0 || config->led_count > LED_COUNT_MAX) {
        return SYNC_ENGINE_ERROR_PARAM;
    }

    if (config->brightness > LED_BRIGHTNESS_MAX) {
        return SYNC_ENGINE_ERROR_PARAM;
    }

    if (config->update_rate == 0 || config->update_rate > 60) {
        return SYNC_ENGINE_ERROR_PARAM;
    }

    return SYNC_ENGINE_OK;
}

/**
 * @brief 更新统计信息
 */
static void sync_engine_update_stats(uint32_t process_time)
{
    g_sync_ctx.stats.total_frames++;
    g_sync_ctx.stats.processed_frames++;

    /* 更新处理时间统计 */
    g_sync_ctx.stats.avg_process_time =
        (g_sync_ctx.stats.avg_process_time * (g_sync_ctx.stats.processed_frames - 1) + process_time) /
        g_sync_ctx.stats.processed_frames;

    if (process_time > g_sync_ctx.stats.max_process_time) {
        g_sync_ctx.stats.max_process_time = process_time;
    }

    if (process_time < g_sync_ctx.stats.min_process_time) {
        g_sync_ctx.stats.min_process_time = process_time;
    }

    /* 计算当前FPS */
    uint32_t current_time = system_get_tick();
    uint32_t time_diff = current_time - g_sync_ctx.last_update_time;
    if (time_diff > 0) {
        g_sync_ctx.stats.current_fps = 1000.0f / time_diff;
    }
}

/**
 * @brief 映射颜色到LED
 */
static void sync_engine_map_colors_to_leds(const color_extract_result_t* result)
{
    if (result == NULL) {
        return;
    }

    switch (g_sync_ctx.config.led_mapping) {
        case LED_MAP_LINEAR:
            sync_engine_apply_linear_mapping(result);
            break;

        case LED_MAP_CIRCULAR:
            sync_engine_apply_circular_mapping(result);
            break;

        case LED_MAP_CUSTOM:
            sync_engine_apply_region_mapping(result);
            break;

        default:
            sync_engine_apply_linear_mapping(result);
            break;
    }
}

/**
 * @brief 应用线性映射
 */
static void sync_engine_apply_linear_mapping(const color_extract_result_t* result)
{
    led_rgb_t color = sync_engine_adjust_color(result->dominant_color);

    /* 所有LED设置为相同颜色 */
    for (uint16_t i = 0; i < g_sync_ctx.config.led_count; i++) {
        led_set_pixel(i, color);
    }
}

/**
 * @brief 应用环形映射
 */
static void sync_engine_apply_circular_mapping(const color_extract_result_t* result)
{
    /* 创建彩虹效果，基于主色调 */
    led_hsv_t base_hsv = led_rgb_to_hsv(result->dominant_color);

    for (uint16_t i = 0; i < g_sync_ctx.config.led_count; i++) {
        led_hsv_t hsv = base_hsv;

        /* 在主色调周围变化 */
        float hue_offset = (float)(i * 60) / g_sync_ctx.config.led_count; /* ±30度范围 */
        hsv.hue = (base_hsv.hue + (uint16_t)hue_offset - 30 + 360) % 360;

        led_rgb_t rgb = led_hsv_to_rgb(hsv);
        rgb = sync_engine_adjust_color(rgb);

        led_set_pixel(i, rgb);
    }
}

/**
 * @brief 应用区域映射
 */
static void sync_engine_apply_region_mapping(const color_extract_result_t* result)
{
    /* 使用自定义区域映射 */
    for (uint8_t i = 0; i < g_sync_ctx.region_count; i++) {
        led_region_map_t* map = &g_sync_ctx.region_maps[i];

        /* 简化处理：所有区域使用主色调 */
        led_rgb_t color = sync_engine_adjust_color(result->dominant_color);

        /* 根据权重调整颜色 */
        color.red = (uint8_t)(color.red * map->weight);
        color.green = (uint8_t)(color.green * map->weight);
        color.blue = (uint8_t)(color.blue * map->weight);

        /* 设置区域内的LED */
        for (uint16_t j = map->led_start; j < map->led_start + map->led_count; j++) {
            if (j < g_sync_ctx.config.led_count) {
                led_set_pixel(j, color);
            }
        }
    }
}

/**
 * @brief 调整颜色
 */
static led_rgb_t sync_engine_adjust_color(led_rgb_t color)
{
    led_rgb_t result = color;

    /* 饱和度增强 */
    if (g_sync_ctx.config.saturation_boost > 0) {
        led_hsv_t hsv = led_rgb_to_hsv(result);

        uint16_t new_saturation = hsv.saturation + g_sync_ctx.config.saturation_boost;
        if (new_saturation > 255) new_saturation = 255;

        hsv.saturation = new_saturation;
        result = led_hsv_to_rgb(hsv);
    }

    /* 色温调节 */
    if (g_sync_ctx.config.color_temperature != 128) {
        float temp_factor = (float)g_sync_ctx.config.color_temperature / 128.0f;

        if (temp_factor > 1.0f) {
            /* 偏暖色 */
            result.red = (uint8_t)(result.red * temp_factor);
            if (result.red > 255) result.red = 255;
        } else {
            /* 偏冷色 */
            result.blue = (uint8_t)(result.blue / temp_factor);
            if (result.blue > 255) result.blue = 255;
        }
    }

    /* 自动亮度调节 */
    if (g_sync_ctx.config.auto_brightness) {
        float brightness = color_calculate_brightness(result);
        uint8_t auto_brightness = (uint8_t)(brightness * 255);

        /* 限制亮度范围 */
        if (auto_brightness < 32) auto_brightness = 32;
        if (auto_brightness > 200) auto_brightness = 200;

        sync_engine_set_brightness(auto_brightness);
    }

    return result;
}

/* ========== BLE命令处理实现 ========== */

/**
 * @brief BLE事件处理
 */
static void sync_engine_handle_ble_event(const ble_event_t* event)
{
    if (event == NULL) {
        return;
    }

    switch (event->type) {
        case BLE_EVENT_CONNECTED:
            DEBUG_INFO("BLE connected");
            break;

        case BLE_EVENT_DISCONNECTED:
            DEBUG_INFO("BLE disconnected");
            break;

        case BLE_EVENT_DATA_RECEIVED:
            if (event->data != NULL && event->length >= sizeof(ble_packet_t)) {
                ble_packet_t* packet = (ble_packet_t*)event->data;
                sync_engine_process_ble_command(packet);
            }
            break;

        default:
            break;
    }
}

/**
 * @brief 处理BLE命令
 */
static int sync_engine_process_ble_command(const ble_packet_t* packet)
{
    if (packet == NULL) {
        return SYNC_ENGINE_ERROR_PARAM;
    }

    DEBUG_DEBUG("Processing BLE command: 0x%02X", packet->command);

    int result = SYNC_ENGINE_OK;

    switch (packet->command) {
        case BLE_CMD_GET_STATUS:
            {
                ble_status_data_t status = {0};
                status.system_state = (uint8_t)g_sync_ctx.state;
                status.sync_enabled = sync_engine_is_running() ? 1 : 0;
                status.led_count = g_sync_ctx.config.led_count;
                status.brightness = g_sync_ctx.config.brightness;
                status.fps = (uint16_t)g_sync_ctx.stats.current_fps;
                status.uptime = system_get_tick() / 1000;
                status.free_memory = system_get_free_heap() / 1024;

                ble_send_response(packet->command, result, (const uint8_t*)&status, sizeof(status));
            }
            break;

        case BLE_CMD_SET_CONFIG:
            if (packet->length >= sizeof(sync_engine_config_t)) {
                sync_engine_config_t* config = (sync_engine_config_t*)packet->data;
                result = sync_engine_set_config(config);
            } else {
                result = SYNC_ENGINE_ERROR_PARAM;
            }
            ble_send_response(packet->command, result, NULL, 0);
            break;

        case BLE_CMD_GET_CONFIG:
            {
                sync_engine_config_t config;
                result = sync_engine_get_config(&config);
                ble_send_response(packet->command, result, (const uint8_t*)&config, sizeof(config));
            }
            break;

        case BLE_CMD_SET_COLOR:
            if (packet->length >= 3) {
                led_rgb_t color = {packet->data[0], packet->data[1], packet->data[2]};
                led_set_all(color);
                led_update();
            } else {
                result = SYNC_ENGINE_ERROR_PARAM;
            }
            ble_send_response(packet->command, result, NULL, 0);
            break;

        case BLE_CMD_SET_BRIGHTNESS:
            if (packet->length >= 1) {
                result = sync_engine_set_brightness(packet->data[0]);
            } else {
                result = SYNC_ENGINE_ERROR_PARAM;
            }
            ble_send_response(packet->command, result, NULL, 0);
            break;

        case BLE_CMD_START_SYNC:
            result = sync_engine_start();
            ble_send_response(packet->command, result, NULL, 0);
            break;

        case BLE_CMD_STOP_SYNC:
            result = sync_engine_stop();
            ble_send_response(packet->command, result, NULL, 0);
            break;

        case BLE_CMD_SET_EFFECT:
            if (packet->length >= sizeof(led_effect_params_t)) {
                led_effect_params_t* effect = (led_effect_params_t*)packet->data;
                result = led_start_effect(effect);
            } else {
                result = SYNC_ENGINE_ERROR_PARAM;
            }
            ble_send_response(packet->command, result, NULL, 0);
            break;

        case BLE_CMD_RESET:
            DEBUG_INFO("System reset requested via BLE");
            ble_send_response(packet->command, result, NULL, 0);
            system_delay_ms(100); /* 等待响应发送完成 */
            system_reset();
            break;

        default:
            result = SYNC_ENGINE_ERROR_NOT_SUPPORT;
            ble_send_response(packet->command, result, NULL, 0);
            break;
    }

    return result;
}

/**
 * @brief 处理BLE命令 (公共接口)
 */
int sync_engine_handle_ble_command(const ble_packet_t* packet)
{
    return sync_engine_process_ble_command(packet);
}

/**
 * @brief 发送状态更新
 */
int sync_engine_send_status_update(void)
{
    if (!ble_is_connected()) {
        return SYNC_ENGINE_ERROR_NOT_READY;
    }

    ble_status_data_t status = {0};
    status.system_state = (uint8_t)g_sync_ctx.state;
    status.sync_enabled = sync_engine_is_running() ? 1 : 0;
    status.led_count = g_sync_ctx.config.led_count;
    status.brightness = g_sync_ctx.config.brightness;
    status.fps = (uint16_t)g_sync_ctx.stats.current_fps;
    status.uptime = system_get_tick() / 1000;
    status.free_memory = system_get_free_heap() / 1024;

    return ble_send_status(&status);
}
